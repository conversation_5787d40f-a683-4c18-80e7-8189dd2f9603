# Firebase Firestore Schema Documentation

> **Note**: Your project uses Google Firebase Firestore, not Supabase. The Firebase MCP tools you mentioned are for Supabase databases.

## Project Details
- **Project ID**: `po2vf2ae7tal9invaj7jkf4a06hsac`
- **Database**: Firebase Firestore
- **Last Updated**: May 28, 2025

## Collections Overview

### 1. `users` Collection
Stores user profiles, preferences, and statistics.

```typescript
interface User {
  uid: string;
  profile: {
    dateOfBirth: Timestamp;
    gender: string;
    height: number;
    weight: number;
    preferredUnits: string; // 'metric' | 'imperial'
  };
  fitness: {
    cardioLevel: number; // 0-1 scale
    strengthLevel: number; // 0-1 scale
    flexibilityLevel: number; // 0-1 scale
    goals: Array<{
      type: string; // 'general_fitness', 'weight_loss', 'muscle_gain', etc.
      priority: number;
    }>;
    exercisesToAvoid: string[];
  };
  preferences: {
    workoutsPerWeek: number;
    durationMinutes: number;
    environments: string[]; // ['home', 'gym', 'homeNoEquipment']
    theme: string; // 'system', 'light', 'dark'
    notifications: boolean;
  };
  stats: {
    totalWorkouts: number;
    totalMinutes: number;
    totalCaloriesBurned: number;
    currentStreak: number;
    lastWorkoutDate: Timestamp;
    weeklyGoal: number; // minutes
    monthlyStats: {
      [yearMonth: string]: { // e.g., "2025-05"
        workouts: number;
        minutes: number;
        calories: number;
      };
    };
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 2. `exercises` Collection
Comprehensive exercise database with instructions and metadata.

```typescript
interface Exercise {
  id: string;
  name: string;
  description: string;
  primaryMuscleGroup: string; // 'chest', 'back', 'legs', 'shoulders', 'arms', 'core'
  secondaryMuscleGroups: string[];
  category: string; // 'strength', 'cardio', 'flexibility', 'hiit'
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  equipment: string[]; // ['barbell', 'dumbbell', 'bodyweight', 'machine', etc.]
  instructions: string[];
  tips: string[];
  caloriesPerMinute: number;
  videoUrl?: string;
  imageUrl?: string;
  muscleGroups: string[]; // All muscles worked
  isPublic: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 3. `workoutHistory` Collection
**🎯 Primary collection for user workout data** - Contains completed workout sessions.

```typescript
interface WorkoutHistory {
  id: string;
  userId: string;
  workoutPlanId?: string; // Reference to workoutPlans collection
  workoutPlanName: string;
  startTime: Timestamp;
  endTime: Timestamp;
  duration: number; // minutes
  exercises: Array<{
    exerciseId: string;
    exerciseName: string;
    setsCompleted: Array<{
      reps: number;
      weight?: number;
      restTaken: number; // seconds
    }>;
    notes?: string;
  }>;
  musclesWorked: string[]; // Primary and secondary muscles
  totalCaloriesBurned: number;
  difficulty: 'easy' | 'moderate' | 'hard';
  overallNotes: string;
  feedback?: {
    difficulty: 'too_easy' | 'just_right' | 'too_hard';
    enjoyment: number; // 1-5 scale
    notes?: string;
  };
  completedAt: Timestamp;
  createdAt: Timestamp;
}
```

### 4. `workoutPlans` Collection
Predefined and user-created workout plans.

```typescript
interface WorkoutPlan {
  id: string;
  name: string;
  description: string;
  category: 'strength' | 'cardio' | 'hiit' | 'yoga' | 'flexibility';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // minutes
  exercises: Array<{
    exerciseId: string;
    sets: number;
    reps: number;
    duration?: number; // for cardio exercises (seconds)
    restTime: number; // seconds
    notes?: {
      name: string;
      equipment: string;
      instructions: string;
      muscleGroups: string[];
    };
  }>;
  createdBy: string; // userId
  isCustom: boolean;
  isPublic: boolean;
  imageUrl?: string;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}
```

### 5. `userWorkouts` Collection
User-specific workout customizations and saved workouts.

```typescript
interface UserWorkout {
  id: string;
  userId: string;
  name: string;
  description?: string;
  exercises: Array<{
    exerciseId: string;
    customSets?: number;
    customReps?: number;
    customWeight?: number;
    notes?: string;
  }>;
  isPublic: boolean;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}
```

### 6. `exerciseAliases` Collection
Alternative names and search terms for exercises.

```typescript
interface ExerciseAlias {
  alias: string; // Document ID
  exerciseId: string; // Reference to exercises collection
  createdAt: Timestamp;
}
```

## Indexes (from firestore.indexes.json)

### Workout History Indexes
- `userId` + `completedAt` (desc) - For fetching user workout history
- `userId` + `workoutPlanId` - For workout plan analytics

### Exercise Indexes
- `primaryMuscleGroup` + `difficulty` - For exercise filtering
- `equipment` (array) + `difficulty` - For equipment-based searches

### User Workout Indexes
- `userId` + `createdAt` (desc) - For user's custom workouts
- `userId` + `isPublic` - For sharing functionality

### Workout Plan Indexes
- `isPublic` + `category` - For public workout discovery
- `difficulty` + `duration` - For workout plan filtering

## Security Rules Summary

- **Users**: Only authenticated users can access their own data
- **Exercises**: Read-only for authenticated users
- **Workout History**: Private to the user who created it
- **Workout Plans**: Public plans readable by all, private plans only by owner
- **User Workouts**: Private to user, with optional public sharing

## Data Deletion Targets

To delete all user workouts, you would need to target:

1. **Primary**: `workoutHistory` collection (filtered by `userId`)
2. **Secondary**: `userWorkouts` collection (filtered by `userId`) 
3. **Optional**: User-created `workoutPlans` (where `createdBy` = `userId` and `isCustom` = true)
4. **Stats Reset**: Reset user stats in `users` collection

## Firebase Admin Operations

Your project uses Firebase Admin SDK with service account:
- File: `po2vf2ae7tal9invaj7jkf4a06hsac-firebase-adminsdk-fbsvc-4c3d1779a6.json`
- Project: `po2vf2ae7tal9invaj7jkf4a06hsac`
