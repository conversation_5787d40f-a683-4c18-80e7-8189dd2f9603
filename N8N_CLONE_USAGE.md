# N8N Workflow Clone - Usage Guide

This document explains how to use the exact N8N workflow clone implemented in your Genkit system.

## Overview

The N8N clone provides three main callable functions that replicate the exact functionality of your N8N workflow:

1. **`n8nMainWorkflow`** - Complete workflow (summarize + create next workout)
2. **`n8nCreateNextWorkoutOnly`** - Just create next workout
3. **`n8nWorkoutSummarizer`** - Just summarize completed workout

## Callable Functions

### 1. N8N Main Workflow

**Function**: `n8nMainWorkflow`

**Purpose**: Replicates the complete N8N workflow - summarizes a just-completed workout and creates the next workout.

**Input**:
```typescript
{
  userId: string,
  requestType: "summarize_and_create_next" | "create_next_only",
  justCompletedWorkout?: {
    plannedWorkout: {
      workout_name: string,
      exercises: Array<{
        name: string,
        sets: number,
        reps: number[],
        weight: number[],
        rest_periods: number
      }>
    },
    actualWorkout: {
      exercises: Array<{
        name: string,
        actual_sets: Array<{
          order: number,
          reps: number,
          weight: number,
          rep_difference: number,
          set_feedback_difficulty: "easy" | "moderate" | "hard"
        }>
      }>
    },
    userWorkoutFeedback: string,
    additionalMetrics: {
      duration: number,
      calories_burned?: number,
      rating: number, // 1-5
      notes?: string
    },
    workoutDate: string
  }
}
```

**Output**:
```typescript
{
  success: boolean,
  workoutSummary?: object, // AI-generated summary
  nextWorkout?: {
    workoutId: string,
    workout_name: string,
    exercises: Array<{
      name: string,
      sets: number,
      reps: number[],
      weight: number[],
      rest_interval: number,
      order_index: number
    }>,
    workout_rationale: string
  },
  message: string
}
```

### 2. Create Next Workout Only

**Function**: `n8nCreateNextWorkoutOnly`

**Purpose**: Creates next workout without summarizing previous workout.

**Input**:
```typescript
{
  userId: string
}
```

**Output**:
```typescript
{
  success: boolean,
  workoutId?: string,
  nextWorkout?: object,
  workoutRationale?: string,
  message: string
}
```

### 3. Workout Summarizer

**Function**: `n8nWorkoutSummarizer`

**Purpose**: Summarizes a completed workout (matches N8N summarizer exactly).

**Input**:
```typescript
{
  userId: string,
  plannedWorkout: object,
  actualWorkout: object,
  userWorkoutFeedback: string,
  additionalMetrics: object,
  workoutDate: string
}
```

**Output**:
```typescript
{
  success: boolean,
  aiSummary?: {
    date: string,
    workout_name: string,
    intro: string,
    planned_vs_actual: Record<string, string[]>,
    feedback_and_metrics: string,
    next_session_recommendations: Record<string, {
      suggestion: string,
      rationale: string
    }>
  },
  message: string
}
```

## Key Features

### ✅ Exact Exercise List
- Uses the same 200+ exercise list from your N8N flow
- Requires exact name matching
- Alphabetically ordered for AI optimization

### ✅ Exact Data Structure
- Arrays for reps and weights per set: `[10, 8, 6]`
- Order index for exercise sequencing
- Rest intervals per exercise
- Exact JSON schema matching N8N

### ✅ Exact AI Prompting
- Replicates N8N system messages
- Same prompt structure and instructions
- Same output format requirements

### ✅ Exact Workflow Logic
- Workout summarization with detailed analysis
- Previous workout summaries integration
- User preferences and fitness guide usage
- 2-second wait between summarization and next workout creation

## Usage Examples

### Complete Workflow (Summarize + Next Workout)
```javascript
const result = await functions().httpsCallable('n8nMainWorkflow')({
  userId: "user123",
  requestType: "summarize_and_create_next",
  justCompletedWorkout: {
    plannedWorkout: {
      workout_name: "Full Body Strength",
      exercises: [{
        name: "Barbell Bench Press",
        sets: 3,
        reps: [10, 8, 6],
        weight: [135, 135, 130],
        rest_periods: 90
      }]
    },
    actualWorkout: {
      exercises: [{
        name: "Barbell Bench Press",
        actual_sets: [{
          order: 1,
          reps: 10,
          weight: 135,
          rep_difference: 0,
          set_feedback_difficulty: "moderate"
        }]
      }]
    },
    userWorkoutFeedback: "Felt strong today",
    additionalMetrics: {
      duration: 60,
      rating: 4
    },
    workoutDate: "2025-01-15"
  }
});
```

### Next Workout Only
```javascript
const result = await functions().httpsCallable('n8nCreateNextWorkoutOnly')({
  userId: "user123"
});
```

## Data Storage

The N8N clone stores data in the following Firestore collections:

- **`completedWorkouts`** - Workout summaries and performance data
- **`userWorkouts`** - Generated workout plans
- **`userProfiles`** - User preferences and fitness data

## Migration from Original Genkit

If you want to switch from your original Genkit implementation to the N8N clone:

1. Update your Flutter app to call the new functions
2. Ensure user profile data includes all required N8N fields
3. Test with the exact data structures shown above

The N8N clone is a complete, standalone implementation that doesn't interfere with your existing Genkit flows.
