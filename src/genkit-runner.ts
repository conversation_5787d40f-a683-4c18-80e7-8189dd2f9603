/**
 * Genkit Runner - Single entry point for all flows
 * This file imports and registers all flows for Genkit UI
 */

// Import shared configuration first
import "./genkit-config";

// Import all flows to register them with Genkit
import "./workout-recommendation-agent";
import "./flows/onboarding-flows-v2";
import "./flows/next-workout-flows-v2";
import "./flows/chat-flow";

// Import RAG components
import {retrievers, indexers, indexSampleDataFlow, testRagRetrievalFlow} from "./rag";

// Export RAG components for Genkit UI
export {retrievers, indexers, indexSampleDataFlow, testRagRetrievalFlow};

console.log("All flows and RAG components loaded successfully!");
