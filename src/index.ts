/**
 * Workout Agent - Firebase Functions Entry Point
 *
 * This file exports Genkit flows for Firebase deployment
 */

import {onRequest} from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";

// Initialize Genkit - IMPORTANT: This must be imported before flows
import "./genkit-config";

// Import all flows to register them with Genkit
import "./workout-recommendation-agent";
import "./flows/onboarding-flows-v2";
import "./flows/next-workout-flows-v2";
import "./flows/chat-flow";

// Export callable functions for Flutter app
export * from "./callable-functions";

// Health check function
export const health = onRequest((_, response) => {
  logger.info("Health check requested", {structuredData: true});
  response.json({
    status: "ok",
    service: "Workout Recommendation Agent",
    timestamp: new Date().toISOString(),
  });
});

// Re-export flows for visibility
export {recommendNextExerciseFlow} from "./workout-recommendation-agent";
export {
  generateFitnessGuideFlow,
  createFirstWorkoutFlow,
  completeOnboardingFlow,
} from "./flows/onboarding-flows-v2";
export {
  analyzeLastWorkoutFlow,
  generateNextWorkoutFlow,
} from "./flows/next-workout-flows-v2";
export {fitnesssChatFlow} from "./flows/chat-flow";
