/**
 * Test RAG Flow
 * Helper flow to test RAG retrieval functionality
 */

import { z } from "genkit";
import { ai } from "../genkit-config";
import { retrieveFitnessContext, formatRetrievedContext } from "./fitness-knowledge-base";

export const testRagRetrievalFlow = ai.defineFlow(
  {
    name: "testRagRetrieval",
    inputSchema: z.object({
      query: z.string().describe("The search query to test"),
      category: z.enum(["exercise", "nutrition", "recovery", "technique", "general"]).optional(),
      limit: z.number().optional().default(5),
    }),
    outputSchema: z.object({
      query: z.string(),
      documentsFound: z.number(),
      formattedContext: z.string(),
      documents: z.array(z.object({
        id: z.string().optional(),
        title: z.string().optional(),
        category: z.string().optional(),
        contentPreview: z.string(),
        tags: z.array(z.string()).optional(),
        score: z.number().optional(),
      })),
    }),
  },
  async (input) => {
    try {
      // Retrieve documents
      const retrievedDocs = await retrieveFitnessContext(input.query, {
        category: input.category,
        limit: input.limit,
      });

      // Format context
      const formattedContext = formatRetrievedContext(retrievedDocs);

      // Process documents for output
      const documents = retrievedDocs.map(doc => ({
        id: doc.metadata?.id || "unknown",
        title: doc.metadata?.title || "Untitled",
        category: doc.metadata?.category || "general",
        contentPreview: doc.content[0]?.text?.substring(0, 200) + "..." || "",
        tags: doc.metadata?.tags || [],
        score: doc.metadata?.score,
      }));

      return {
        query: input.query,
        documentsFound: retrievedDocs.length,
        formattedContext: formattedContext,
        documents: documents,
      };
    } catch (error) {
      console.error("RAG test error:", error);
      return {
        query: input.query,
        documentsFound: 0,
        formattedContext: "Error retrieving documents: " + error,
        documents: [],
      };
    }
  }
);