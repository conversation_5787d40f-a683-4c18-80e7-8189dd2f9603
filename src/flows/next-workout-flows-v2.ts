/**
 * Next Workout Generation Flows V2 - Using DotPrompt
 * Creates personalized workouts based on user progress and recovery
 */

import {z} from "genkit";
import * as admin from "firebase-admin";
import {ai, db} from "../genkit-config";
import {
  getUserProfileTool,
  analyzeWorkoutHistoryTool,
  searchExercisesTool,
} from "../workout-recommendation-agent";
import {getPreviousWorkoutSummariesFlow} from "./enhanced-workout-analysis";

// Note: Prompts will be loaded within each flow using ai.prompt()

/**
 * Analyze last workout performance
 */
export const analyzeLastWorkoutFlow = ai.defineFlow(
  {
    name: "analyzeLastWorkout",
    inputSchema: z.object({
      userId: z.string(),
      workoutId: z.string().optional(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      analysis: z.any().optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Get last workout
      let workout: any;

      if (input.workoutId) {
        const workoutDoc = await db.collection("workoutHistory").doc(input.workoutId).get();
        if (!workoutDoc.exists) {
          throw new Error("Workout not found");
        }
        workout = {id: workoutDoc.id, ...workoutDoc.data()};
      } else {
        const query = db.collection("workoutHistory")
          .where("userId", "==", input.userId)
          .orderBy("completedAt", "desc")
          .limit(1);

        const snapshot = await query.get();
        if (snapshot.empty) {
          return {
            success: false,
            message: "No workout history found. Complete your first workout to get started!",
          };
        }

        workout = {id: snapshot.docs[0].id, ...snapshot.docs[0].data()};
      }

      // Get user profile for context
      const userProfile = await getUserProfileTool({userId: input.userId});

      // Calculate hours since workout
      const hoursSinceWorkout = (Date.now() - workout.completedAt.toMillis()) / (1000 * 60 * 60);

      // Analyze using DotPrompt
      const workoutAnalysisPrompt = ai.prompt("workout-analysis");
      const analysisResult = await workoutAnalysisPrompt({
        input: {
          workout: {
            workoutPlanName: workout.workoutPlanName,
            duration: workout.duration,
            difficulty: workout.difficulty,
            musclesWorked: workout.musclesWorked || [],
            exercises: workout.exercises || [],
          },
          userProfile: {
            fitness: userProfile.fitness,
          },
          hoursSinceWorkout,
        },
      });

      const {output: analysis} = analysisResult;

      return {
        success: true,
        analysis: {
          ...analysis,
          musclesWorked: workout.musclesWorked || [],
        },
        message: "Workout analysis complete. Use this to plan your next session.",
      };
    } catch (error) {
      console.error("Error analyzing workout:", error);
      return {
        success: false,
        message: `Failed to analyze workout: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);

/**
 * Generate next workout based on recovery and progress
 */
export const generateNextWorkoutFlow = ai.defineFlow(
  {
    name: "generateNextWorkout",
    inputSchema: z.object({
      userId: z.string(),
      skipRecoveryCheck: z.boolean().optional(),
      targetMuscles: z.array(z.string()).optional(),
      workoutType: z.enum(["strength", "cardio", "mixed", "flexibility"]).optional(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      workoutId: z.string().optional(),
      workout: z.any().optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Get user profile, history, and previous workout summaries
      const [userProfile, workoutHistory, previousSummaries] = await Promise.all([
        getUserProfileTool({userId: input.userId}),
        analyzeWorkoutHistoryTool({userId: input.userId, daysToAnalyze: 14}),
        getPreviousWorkoutSummariesFlow({userId: input.userId, limit: 3}),
      ]);

      // Analyze last workout if exists
      let lastWorkoutAnalysis = null;
      if (workoutHistory.lastWorkoutDate) {
        const analysisResult = await analyzeLastWorkoutFlow({userId: input.userId});
        if (analysisResult.success) {
          lastWorkoutAnalysis = analysisResult.analysis;
        }
      }

      // Check recovery status
      const hoursSinceLastWorkout = workoutHistory.lastWorkoutDate ?
        (Date.now() - new Date(workoutHistory.lastWorkoutDate).getTime()) / (1000 * 60 * 60) :
        72;

      const isRecovered = input.skipRecoveryCheck ||
        hoursSinceLastWorkout >= (lastWorkoutAnalysis?.recoveryNeeded || 48);

      if (!isRecovered && !input.skipRecoveryCheck) {
        const hoursUntilRecovered = (lastWorkoutAnalysis?.recoveryNeeded || 48) - hoursSinceLastWorkout;
        return {
          success: false,
          message: `Your muscles need more recovery time. Wait ${Math.round(hoursUntilRecovered)} more hours for optimal results, or do light cardio/stretching instead.`,
        };
      }

      // Determine muscles to target
      const targetMuscles = input.targetMuscles ||
        lastWorkoutAnalysis?.recommendations.focusMuscles ||
        identifyUnderworkedMuscles(workoutHistory.muscleGroupFrequency);

      // Get appropriate exercises
      const exercises = await searchExercisesTool({
        muscleGroups: targetMuscles.slice(0, 3),
        equipment: userProfile.preferences.environments.includes("homeNoEquipment") ?
          ["bodyweight"] :
          ["barbell", "dumbbell", "bodyweight"],
        difficulty: determineWorkoutDifficulty(userProfile.fitness, lastWorkoutAnalysis),
        excludeExercises: [
          ...userProfile.fitness.exercisesToAvoid,
          ...workoutHistory.recentExercises.slice(0, 3).map((e) => e.exerciseId),
        ],
        limit: 15,
      });

      // Generate workout using DotPrompt
      const nextWorkoutCreatorPrompt = ai.prompt("next-workout-creator");
      const workoutResult = await nextWorkoutCreatorPrompt({
        input: {
          userProfile: {
            fitness: userProfile.fitness,
            preferences: {
              durationMinutes: userProfile.preferences.durationMinutes,
            },
          },
          workoutHistory: {
            totalWorkouts: workoutHistory.totalWorkouts,
            averageFrequency: workoutHistory.averageFrequency,
            recentExercises: workoutHistory.recentExercises,
          },
          lastWorkoutAnalysis,
          previousWorkoutSummaries: previousSummaries.summaries,
          hoursSinceLastWorkout,
          targetMuscles,
          availableExercises: exercises.map((ex) => ({
            id: ex.id,
            name: ex.name,
            primaryMuscleGroup: ex.primaryMuscleGroup,
            difficulty: ex.difficulty,
          })),
          workoutType: input.workoutType,
        },
      });

      const {output: workout} = workoutResult;

      // Map exercises and calculate duration
      const workoutExercises = workout.exercises.map((ex: any) => {
        const matchingExercise = exercises.find((e) =>
          e.name === ex.name ||
          e.name.toLowerCase() === ex.name.toLowerCase()
        );

        // Ensure reps and weight are arrays matching the number of sets
        const repsArray = Array.isArray(ex.reps) ? ex.reps : Array(ex.sets).fill(parseInt(ex.reps) || 12);
        const weightArray = Array.isArray(ex.weight) ? ex.weight : Array(ex.sets).fill(ex.weight || 0);

        // Fallback if no matching exercise found
        if (!matchingExercise) {
          return {
            exerciseId: `custom-${ex.name.toLowerCase().replace(/\s+/g, "-")}`,
            exerciseName: ex.name,
            sets: ex.sets,
            reps: repsArray,
            weight: weightArray,
            restSeconds: ex.restSeconds,
            notes: ex.notes,
            orderIndex: ex.orderIndex || 1,
          };
        }

        return {
          exerciseId: matchingExercise.id,
          exerciseName: matchingExercise.name,
          sets: ex.sets,
          reps: repsArray,
          weight: weightArray,
          restSeconds: ex.restSeconds,
          notes: ex.notes,
          orderIndex: ex.orderIndex || 1,
        };
      });

      const estimatedDuration = workoutExercises.reduce((total: number, ex: any) => {
        const exerciseTime = (ex.sets * 45) + ((ex.sets - 1) * ex.restSeconds);
        return total + (exerciseTime / 60);
      }, 5);

      // Save workout
      const workoutData = {
        userId: input.userId,
        name: workout.name,
        description: workout.description,
        category: input.workoutType || "mixed",
        difficulty: workout.difficulty as any,
        duration: Math.round(estimatedDuration),
        exercises: workoutExercises.map((ex: any) => ({
          exerciseId: ex.exerciseId,
          notes: {
            name: ex.exerciseName,
            equipment: "varies",
            targetMuscles: workout.targetMuscles,
            instructions: ex.notes,
          },
          sets: ex.sets,
          reps: ex.reps, // Now an array
          weight: ex.weight, // Now an array
          restTime: ex.restSeconds,
          duration: 0,
          orderIndex: ex.orderIndex,
        })),
        equipment: exercises.flatMap((e) => e.equipment).filter((v, i, a) => a.indexOf(v) === i),
        targetMuscleGroups: workout.targetMuscles,
        isCustom: true,
        isPublic: false,
        tags: ["ai-generated", "progressive", workout.difficulty],
        imageUrl: "https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400",
        createdBy: input.userId,
        metadata: {
          generatedAt: admin.firestore.Timestamp.now(),
          rationale: workout.rationale,
          recommendedTiming: workout.recommendedTiming,
        },
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      };

      const workoutRef = await db.collection("userWorkouts").add(workoutData);

      return {
        success: true,
        workoutId: workoutRef.id,
        workout: {
          ...workout,
          exercises: workoutExercises,
          estimatedDuration: Math.round(estimatedDuration),
        },
        message: `${workout.name} is ready! ${workout.recommendedTiming}`,
      };
    } catch (error) {
      console.error("Error generating workout:", error);
      return {
        success: false,
        message: `Failed to generate workout: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);

// Helper functions
function identifyUnderworkedMuscles(muscleGroupFrequency: Record<string, number>): string[] {
  const allMuscleGroups = ["chest", "back", "shoulders", "arms", "legs", "core", "glutes"];
  const frequencies = allMuscleGroups.map((muscle) => ({
    muscle,
    frequency: muscleGroupFrequency[muscle] || 0,
  }));

  frequencies.sort((a, b) => a.frequency - b.frequency);
  return frequencies.slice(0, 3).map((f) => f.muscle);
}

function determineWorkoutDifficulty(fitness: any, lastAnalysis: any): "beginner" | "intermediate" | "advanced" {
  const baseLevel = (fitness.strengthLevel + fitness.cardioLevel) / 2;

  if (lastAnalysis) {
    if (lastAnalysis.performanceRating >= 8 && lastAnalysis.recommendations.volumeAdjustment === "increase") {
      if (baseLevel < 0.33) return "intermediate";
      if (baseLevel < 0.67) return "advanced";
    }
    if (lastAnalysis.performanceRating < 5 && lastAnalysis.recommendations.volumeAdjustment === "decrease") {
      if (baseLevel > 0.67) return "intermediate";
      if (baseLevel > 0.33) return "beginner";
    }
  }

  if (baseLevel < 0.33) return "beginner";
  if (baseLevel < 0.67) return "intermediate";
  return "advanced";
}
