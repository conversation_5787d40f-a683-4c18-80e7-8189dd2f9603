/**
 * Onboarding Flows V2 - Using DotPrompt
 * Flows that run after user completes onboarding
 */

import {z} from "genkit";
import * as admin from "firebase-admin";
import {ai, db} from "../genkit-config";
import {getUserProfileTool} from "../workout-recommendation-agent";

// Note: Prompts will be loaded within each flow using ai.prompt()

/**
 * Generate personalized fitness guide after onboarding
 */
export const generateFitnessGuideFlow = ai.defineFlow(
  {
    name: "generateFitnessGuide",
    inputSchema: z.object({
      userId: z.string(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      guideId: z.string().optional(),
      guide: z.any().optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Get user profile
      const userProfile = await getUserProfileTool({userId: input.userId});

      // Generate fitness guide using DotPrompt
      const fitnessResearchPrompt = ai.prompt("fitness-research");
      const guideResult = await fitnessResearchPrompt({
        input: {
          userId: input.userId,
          profile: {
            name: userProfile.profile.gender === "male" ? "Sir" : "Madam",
            gender: userProfile.profile.gender,
            age: userProfile.profile.age,
            height: userProfile.profile.height,
            weight: userProfile.profile.weight,
            preferredUnits: userProfile.profile.preferredUnits,
          },
          fitness: userProfile.fitness,
          preferences: {
            workoutsPerWeek: userProfile.preferences.workoutsPerWeek,
            durationMinutes: userProfile.preferences.durationMinutes,
            environments: userProfile.preferences.environments,
          },
        },
      });

      const {output: guide} = guideResult;

      // Save guide to user's profile
      const guideData = {
        userId: input.userId,
        guide,
        generatedAt: admin.firestore.Timestamp.now(),
        version: 1,
      };

      const guideRef = await db.collection("userFitnessGuides").add(guideData);

      // Update user profile
      await db.collection("users").doc(input.userId).update({
        "fitness.hasPersonalizedGuide": true,
        "fitness.guideId": guideRef.id,
        "updatedAt": admin.firestore.Timestamp.now(),
      });

      return {
        success: true,
        guideId: guideRef.id,
        guide,
        message: "Personalized fitness guide generated successfully!",
      };
    } catch (error) {
      console.error("Error generating fitness guide:", error);
      return {
        success: false,
        message: `Failed to generate guide: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);

/**
 * Create first workout after onboarding
 */
export const createFirstWorkoutFlow = ai.defineFlow(
  {
    name: "createFirstWorkout",
    inputSchema: z.object({
      userId: z.string(),
      guideId: z.string().optional(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      workoutId: z.string().optional(),
      workout: z.any().optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Get user profile
      const userProfile = await getUserProfileTool({userId: input.userId});

      // Get appropriate exercises
      const exerciseQuery = db.collection("exercises")
        .where("difficulty", "==", userProfile.fitness.strengthLevel < 0.33 ? "beginner" : "intermediate")
        .limit(20);

      const exercisesSnapshot = await exerciseQuery.get();
      const availableExercises = exercisesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Filter by equipment
      const equipment = userProfile.preferences.environments.includes("homeNoEquipment") ?
        [] : ["dumbbell", "barbell"];

      const suitableExercises = availableExercises.filter((ex) => {
        if (equipment.length === 0 && (ex as any).equipment && (ex as any).equipment.length > 0) return false;
        if (userProfile.fitness.exercisesToAvoid.includes(ex.id)) return false;
        return true;
      });

      // Generate first workout using DotPrompt
      const firstWorkoutPrompt = ai.prompt("first-workout");
      const workoutResult = await firstWorkoutPrompt({
        input: {
          userProfile: {
            fitness: userProfile.fitness,
            preferences: userProfile.preferences,
          },
          availableExercises: suitableExercises.slice(0, 15).map((ex: any) => ({
            id: ex.id,
            name: ex.name,
            primaryMuscleGroup: ex.primaryMuscleGroup,
            difficulty: ex.difficulty,
            equipment: ex.equipment,
          })),
        },
      });

      const {output: workout} = workoutResult;

      // Map exercise names to IDs
      const workoutExercises = workout.exercises.map((ex: any) => {
        const matchingExercise = suitableExercises.find((e: any) =>
          e.name === ex.name ||
          e.name.toLowerCase() === ex.name.toLowerCase()
        ) as any;

        return {
          exerciseId: matchingExercise?.id || "unknown",
          exerciseName: matchingExercise?.name || ex.name,
          sets: ex.sets,
          reps: ex.reps,
          restSeconds: ex.restSeconds,
          notes: ex.notes,
        };
      });

      // Calculate estimated duration
      const estimatedDuration = workoutExercises.reduce((total: number, ex: any) => {
        const exerciseTime = (ex.sets * 45) + ((ex.sets - 1) * ex.restSeconds);
        return total + (exerciseTime / 60);
      }, 5);

      // Save workout
      const workoutData = {
        userId: input.userId,
        name: workout.name,
        description: workout.description,
        category: "mixed",
        difficulty: userProfile.fitness.strengthLevel < 0.33 ? "beginner" : "intermediate",
        duration: Math.round(estimatedDuration),
        exercises: workoutExercises.map((ex: any) => ({
          exerciseId: ex.exerciseId,
          notes: {
            name: ex.exerciseName,
            equipment: "bodyweight",
            targetMuscles: workout.targetMuscles,
            instructions: ex.notes,
          },
          sets: ex.sets,
          reps: parseInt(ex.reps) || 12,
          restTime: ex.restSeconds,
          duration: 0,
        })),
        equipment: equipment,
        targetMuscleGroups: workout.targetMuscles,
        isCustom: true,
        isPublic: false,
        tags: ["first-workout", "onboarding", "ai-generated"],
        imageUrl: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400",
        createdBy: input.userId,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      };

      const workoutRef = await db.collection("userWorkouts").add(workoutData);

      return {
        success: true,
        workoutId: workoutRef.id,
        workout: {
          ...workout,
          exercises: workoutExercises,
          estimatedDuration: Math.round(estimatedDuration),
        },
        message: "Your first workout is ready! Remember to warm up and focus on form.",
      };
    } catch (error) {
      console.error("Error creating first workout:", error);
      return {
        success: false,
        message: `Failed to create workout: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);

/**
 * Complete onboarding flow
 */
export const completeOnboardingFlow = ai.defineFlow(
  {
    name: "completeOnboarding",
    inputSchema: z.object({
      userId: z.string(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      guideId: z.string().optional(),
      workoutId: z.string().optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Generate fitness guide
      const guideResult = await generateFitnessGuideFlow({userId: input.userId});

      if (!guideResult.success) {
        throw new Error("Failed to generate fitness guide");
      }

      // Create first workout
      const workoutResult = await createFirstWorkoutFlow({
        userId: input.userId,
        guideId: guideResult.guideId,
      });

      if (!workoutResult.success) {
        throw new Error("Failed to create first workout");
      }

      // Mark onboarding as complete
      await db.collection("users").doc(input.userId).update({
        "onboardingCompleted": true,
        "onboardingCompletedAt": admin.firestore.Timestamp.now(),
        "updatedAt": admin.firestore.Timestamp.now(),
      });

      return {
        success: true,
        guideId: guideResult.guideId,
        workoutId: workoutResult.workoutId,
        message: "Welcome to your fitness journey! Your personalized guide and first workout are ready.",
      };
    } catch (error) {
      console.error("Error completing onboarding:", error);
      return {
        success: false,
        message: `Onboarding failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);
