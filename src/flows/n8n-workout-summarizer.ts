/**
 * N8N Workout Summarizer - Exact Clone
 * Replicates the workout summarization logic from N8N flow
 */

import {z} from "genkit";
import * as admin from "firebase-admin";
import {ai, db} from "../genkit-config";

/**
 * Exact clone of N8N workout summarizer
 */
export const n8nWorkoutSummarizerFlow = ai.defineFlow(
  {
    name: "n8nWorkoutSummarizer",
    inputSchema: z.object({
      userId: z.string(),
      plannedWorkout: z.object({
        workout_name: z.string(),
        exercises: z.array(z.object({
          name: z.string(),
          sets: z.number(),
          reps: z.array(z.number()),
          weight: z.array(z.number()),
          rest_periods: z.number(),
        })),
      }),
      actualWorkout: z.object({
        exercises: z.array(z.object({
          name: z.string(),
          actual_sets: z.array(z.object({
            order: z.number(),
            reps: z.number(),
            weight: z.number(),
            rep_difference: z.number(),
            set_feedback_difficulty: z.enum(["easy", "moderate", "hard"]),
          })),
        })),
      }),
      userWorkoutFeedback: z.string(),
      additionalMetrics: z.object({
        duration: z.number(),
        calories_burned: z.number().optional(),
        rating: z.number().min(1).max(5),
        notes: z.string().optional(),
      }),
      workoutDate: z.string(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      aiSummary: z.object({
        date: z.string(),
        workout_name: z.string(),
        intro: z.string(),
        planned_vs_actual: z.record(z.array(z.string())),
        feedback_and_metrics: z.string(),
        next_session_recommendations: z.record(z.object({
          suggestion: z.string(),
          rationale: z.string(),
        })),
      }).optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Get user preferences for context
      const userDoc = await db.collection("userProfiles").doc(input.userId).get();
      if (!userDoc.exists) {
        throw new Error("User profile not found");
      }
      const userProfile = userDoc.data();

      // Create the exact prompt from N8N
      const summarizerPrompt = ai.prompt("n8n-workout-summarizer");
      const summaryResult = await summarizerPrompt({
        input: {
          planned_workout: input.plannedWorkout,
          workout_date: input.workoutDate,
          actual_workout: input.actualWorkout,
          user_workout_feedback: input.userWorkoutFeedback,
          additional_metrics: input.additionalMetrics,
          user_preferences: {
            goals: userProfile?.fitness?.goals || [],
            fitness_level: {
              strength: userProfile?.fitness?.strengthLevel || 0.5,
              cardio: userProfile?.fitness?.cardioLevel || 0.5,
            },
            fitness_guide: userProfile?.fitnessGuide || "",
          },
        },
      });

      const {output: aiSummary} = summaryResult;

      // Save summary to completed workouts (matching N8N structure)
      await db.collection("completedWorkouts").add({
        userId: input.userId,
        workoutDate: input.workoutDate,
        plannedWorkout: input.plannedWorkout,
        actualWorkout: input.actualWorkout,
        userFeedback: input.userWorkoutFeedback,
        additionalMetrics: input.additionalMetrics,
        aiSummary,
        createdAt: admin.firestore.Timestamp.now(),
      });

      return {
        success: true,
        aiSummary,
        message: "Workout summary created successfully",
      };
    } catch (error) {
      console.error("Error in N8N workout summarizer:", error);
      return {
        success: false,
        message: `Failed to create workout summary: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);

/**
 * Get previous workout summaries (last 3) - matching N8N logic
 */
export const getPreviousWorkoutSummariesN8NFlow = ai.defineFlow(
  {
    name: "getPreviousWorkoutSummariesN8N",
    inputSchema: z.object({
      userId: z.string(),
      limit: z.number().default(3),
    }),
    outputSchema: z.object({
      summaries: z.array(z.object({
        date: z.string(),
        summary: z.any(),
      })),
    }),
  },
  async (input) => {
    const query = db.collection("completedWorkouts")
      .where("userId", "==", input.userId)
      .orderBy("createdAt", "desc")
      .limit(input.limit);

    const snapshot = await query.get();
    
    const summaries = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        date: data.workoutDate,
        summary: data.aiSummary,
      };
    });

    return {summaries};
  }
);
