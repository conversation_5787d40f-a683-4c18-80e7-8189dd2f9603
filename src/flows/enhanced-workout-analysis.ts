/**
 * Enhanced Workout Analysis Flow
 * Provides detailed workout summarization matching N8N flow capabilities
 */

import {z} from "genkit";
import * as admin from "firebase-admin";
import {ai, db} from "../genkit-config";

/**
 * Enhanced workout analysis with detailed planned vs actual comparison
 */
export const enhancedWorkoutAnalysisFlow = ai.defineFlow(
  {
    name: "enhancedWorkoutAnalysis",
    inputSchema: z.object({
      userId: z.string(),
      workoutId: z.string(),
      plannedWorkout: z.object({
        name: z.string(),
        exercises: z.array(z.object({
          name: z.string(),
          sets: z.number(),
          reps: z.array(z.number()),
          weight: z.array(z.number()),
          restTime: z.number(),
        })),
      }),
      actualWorkout: z.object({
        exercises: z.array(z.object({
          name: z.string(),
          setsCompleted: z.array(z.object({
            reps: z.number(),
            weight: z.number(),
            difficulty: z.enum(["easy", "moderate", "hard"]),
            repDifference: z.number(),
          })),
        })),
      }),
      userFeedback: z.string().optional(),
      additionalMetrics: z.object({
        duration: z.number(),
        caloriesBurned: z.number().optional(),
        overallRating: z.number().min(1).max(5),
        notes: z.string().optional(),
      }),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      analysis: z.object({
        date: z.string(),
        workoutName: z.string(),
        intro: z.string(),
        plannedVsActual: z.record(z.array(z.string())),
        feedbackAndMetrics: z.string(),
        nextSessionRecommendations: z.record(z.object({
          suggestion: z.string(),
          rationale: z.string(),
        })),
      }).optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Get user profile for context
      const userDoc = await db.collection("userProfiles").doc(input.userId).get();
      if (!userDoc.exists) {
        throw new Error("User profile not found");
      }
      const userProfile = userDoc.data();

      // Create enhanced workout analysis prompt
      const analysisPrompt = ai.prompt("enhanced-workout-analysis");
      const analysisResult = await analysisPrompt({
        input: {
          workoutName: input.plannedWorkout.name,
          workoutDate: new Date().toISOString().split('T')[0],
          plannedWorkout: input.plannedWorkout,
          actualWorkout: input.actualWorkout,
          userFeedback: input.userFeedback || "",
          additionalMetrics: input.additionalMetrics,
          userPreferences: {
            goals: userProfile?.fitness?.goals || [],
            fitnessLevel: {
              strength: userProfile?.fitness?.strengthLevel || 0.5,
              cardio: userProfile?.fitness?.cardioLevel || 0.5,
            },
          },
        },
      });

      const {output: analysis} = analysisResult;

      // Save analysis to workout history
      await db.collection("workoutHistory").doc(input.workoutId).update({
        aiAnalysis: analysis,
        analyzedAt: admin.firestore.Timestamp.now(),
      });

      return {
        success: true,
        analysis,
        message: "Enhanced workout analysis completed successfully",
      };
    } catch (error) {
      console.error("Error in enhanced workout analysis:", error);
      return {
        success: false,
        message: `Failed to analyze workout: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);

/**
 * Get previous workout summaries for context
 */
export const getPreviousWorkoutSummariesFlow = ai.defineFlow(
  {
    name: "getPreviousWorkoutSummaries",
    inputSchema: z.object({
      userId: z.string(),
      limit: z.number().default(3),
    }),
    outputSchema: z.object({
      summaries: z.array(z.object({
        date: z.string(),
        summary: z.any(),
      })),
    }),
  },
  async (input) => {
    const query = db.collection("workoutHistory")
      .where("userId", "==", input.userId)
      .where("aiAnalysis", "!=", null)
      .orderBy("completedAt", "desc")
      .limit(input.limit);

    const snapshot = await query.get();
    
    const summaries = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        date: data.completedAt.toDate().toISOString().split('T')[0],
        summary: data.aiAnalysis,
      };
    });

    return {summaries};
  }
);
