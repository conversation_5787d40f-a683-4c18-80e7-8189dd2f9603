/**
 * N8N Next Workout Creator - Exact Clone
 * Replicates the next workout creation logic from N8N flow
 */

import {z} from "genkit";
import * as admin from "firebase-admin";
import {ai, db} from "../genkit-config";
import {EXERCISE_LIST} from "../data/exercise-list";
import {getPreviousWorkoutSummariesN8NFlow} from "./n8n-workout-summarizer";

/**
 * Exact clone of N8N next workout creator
 */
export const n8nNextWorkoutCreatorFlow = ai.defineFlow(
  {
    name: "n8nNextWorkoutCreator",
    inputSchema: z.object({
      userId: z.string(),
      justFinishedWorkoutAiSummary: z.any().optional(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      workoutId: z.string().optional(),
      nextWorkout: z.object({
        workout_name: z.string(),
        exercises: z.array(z.object({
          name: z.string(),
          sets: z.number(),
          reps: z.array(z.number()),
          weight: z.array(z.number()),
          rest_interval: z.number(),
          order_index: z.number(),
        })),
      }).optional(),
      workoutRationale: z.string().optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Get user profile and preferences
      const userDoc = await db.collection("userProfiles").doc(input.userId).get();
      if (!userDoc.exists) {
        throw new Error("User profile not found");
      }
      const userProfile = userDoc.data();

      // Get previous workout summaries (last 3)
      const previousSummariesResult = await getPreviousWorkoutSummariesN8NFlow({
        userId: input.userId,
        limit: 3,
      });

      // Get fitness guide
      const fitnessGuide = userProfile?.fitnessGuide || "";

      // Prepare user preferences exactly as N8N does
      const userPreferences = {
        display_name: userProfile?.displayName || "",
        age: userProfile?.age || 0,
        gender: userProfile?.gender || "",
        height: userProfile?.height || 0,
        weight: userProfile?.weight || 0,
        height_unit: userProfile?.heightUnit || "cm",
        weight_unit: userProfile?.weightUnit || "kg",
        primarygoal: userProfile?.fitness?.goals?.[0]?.type || "",
        fitnessgoals: userProfile?.fitness?.goals || [],
        cardiolevel: userProfile?.fitness?.cardioLevel || 0.5,
        weightliftinglevel: userProfile?.fitness?.strengthLevel || 0.5,
        equipment: userProfile?.preferences?.equipment || [],
        workoutdays: userProfile?.preferences?.workoutDays || [],
        workoutduration: userProfile?.preferences?.durationMinutes || 60,
        workoutfrequency: userProfile?.preferences?.frequency || 3,
        excluded_exercises: userProfile?.fitness?.exercisesToAvoid || [],
        additional_notes: userProfile?.additionalNotes || "",
        sport_activity: userProfile?.sportActivity || "",
        weightlifting_level_description: userProfile?.weightliftingLevelDescription || "",
        cardio_level_description: userProfile?.cardioLevelDescription || "",
      };

      // Create the exact prompt input as N8N
      const nextWorkoutPrompt = ai.prompt("n8n-next-workout-creator");
      const workoutResult = await nextWorkoutPrompt({
        input: {
          fitness_guide: fitnessGuide,
          just_finished_workout_ai_summary: input.justFinishedWorkoutAiSummary || null,
          previous_workout_summaries_and_dates: previousSummariesResult.summaries,
          user_preferences: userPreferences,
          exercise_list: EXERCISE_LIST,
        },
      });

      const {output: workoutOutput} = workoutResult;

      // Extract next_workout and workout_rationale from output
      const nextWorkout = workoutOutput.next_workout;
      const workoutRationale = workoutOutput.workout_rationale;

      // Create workout ID for tracking
      const workoutId = `workout_${Date.now()}_${input.userId}`;

      // Save workout to userWorkouts collection (matching your existing structure)
      const workoutData = {
        userId: input.userId,
        name: nextWorkout.workout_name,
        description: workoutRationale,
        category: "ai-generated",
        difficulty: "adaptive",
        duration: userPreferences.workoutduration,
        exercises: nextWorkout.exercises.map((ex: any, index: number) => ({
          exerciseId: `exercise_${ex.name.toLowerCase().replace(/\s+/g, "_")}`,
          notes: {
            name: ex.name,
            equipment: "varies",
            targetMuscles: [],
            instructions: "",
          },
          sets: ex.sets,
          reps: ex.reps, // Array format
          weight: ex.weight, // Array format
          restTime: ex.rest_interval,
          duration: 0,
          orderIndex: ex.order_index,
        })),
        equipment: userPreferences.equipment,
        targetMuscleGroups: [],
        isCustom: true,
        isPublic: false,
        tags: ["ai-generated", "n8n-clone", "progressive"],
        imageUrl: "https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400",
        createdBy: input.userId,
        metadata: {
          generatedAt: admin.firestore.Timestamp.now(),
          rationale: workoutRationale,
          source: "n8n-clone",
        },
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      };

      const workoutRef = await db.collection("userWorkouts").add(workoutData);

      return {
        success: true,
        workoutId: workoutRef.id,
        nextWorkout,
        workoutRationale,
        message: `${nextWorkout.workout_name} is ready! Your personalized workout has been created.`,
      };
    } catch (error) {
      console.error("Error in N8N next workout creator:", error);
      return {
        success: false,
        message: `Failed to create next workout: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);
