/**
 * Chat Flow with RAG
 * General fitness chat with context-aware responses
 */

import {z} from "genkit";
import * as admin from "firebase-admin";
import {ai, db} from "../genkit-config";
import {getUserProfileTool, analyzeWorkoutHistoryTool} from "../workout-recommendation-agent";
import {retrieveFitnessContext, formatRetrievedContext} from "../rag/fitness-knowledge-base";

// Define the chat flow
export const fitnesssChatFlow = ai.defineFlow(
  {
    name: "fitnessChat",
    inputSchema: z.object({
      userId: z.string(),
      message: z.string(),
      conversationId: z.string().optional(),
    }),
    outputSchema: z.object({
      response: z.string(),
      conversationId: z.string().optional(),
      suggestions: z.array(z.string()).optional(),
      relatedExercises: z.array(z.object({
        id: z.string(),
        name: z.string(),
        relevance: z.string(),
      })).optional(),
    }),
  },
  async (input) => {
    try {
      // 1. Get user context
      const userProfile = await getUserProfileTool({userId: input.userId});
      const workoutHistory = await analyzeWorkoutHistoryTool({
        userId: input.userId,
        daysToAnalyze: 30,
      });

      // 2. Retrieve relevant context using RAG
      const retrievedDocs = await retrieveFitnessContext(input.message, {
        limit: 5,
      });

      const ragContext = formatRetrievedContext(retrievedDocs);

      const context = {
        userGoals: userProfile.fitness.goals,
        fitnessLevel: {
          strength: userProfile.fitness.strengthLevel,
          cardio: userProfile.fitness.cardioLevel,
        },
        recentWorkouts: workoutHistory.recentExercises,
        preferences: userProfile.preferences,
        limitations: userProfile.fitness.exercisesToAvoid,
      };

      // 3. Generate response
      const chatResponse = await ai.generate({
        model: "googleai/gemini-1.5-flash",
        prompt: `You are a knowledgeable fitness coach having a conversation with a user.

User Profile:
- Goals: ${JSON.stringify(context.userGoals)}
- Fitness Level: Strength ${context.fitnessLevel.strength}, Cardio ${context.fitnessLevel.cardio}
- Recent Workouts: ${context.recentWorkouts}
- Preferences: ${JSON.stringify(context.preferences)}
- Limitations: ${JSON.stringify(context.limitations)}

Relevant Fitness Knowledge:
${ragContext}

User Message: ${input.message}

Provide a helpful, personalized response that:
1. Addresses their specific question using the retrieved knowledge when relevant
2. Considers their fitness level and goals
3. Is encouraging and supportive
4. Suggests practical next steps if relevant
5. References their recent workout history when applicable
6. Incorporates accurate information from the knowledge base

Keep the response conversational and concise (2-3 paragraphs max).`,
        output: {
          format: "json",
          schema: z.object({
            response: z.string(),
            suggestedFollowUps: z.array(z.string()).optional(),
            relatedExerciseIds: z.array(z.string()).optional(),
          }),
        },
      });

      // 4. Create or use conversation ID
      const conversationId = input.conversationId || db.collection("conversations").doc().id;
      // 5. Save conversation for future context
      if (chatResponse.output) {
        await db.collection("conversations").doc(conversationId).collection("messages").add({
          userId: input.userId,
          message: input.message,
          response: chatResponse.output.response,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
      }

      // Extract related exercises from retrieved documents
      const relatedExercises = retrievedDocs
        .filter((doc) => doc.metadata?.category === "exercise" || doc.metadata?.category === "technique")
        .map((doc) => ({
          id: doc.metadata?.id || "unknown",
          name: doc.metadata?.title || "Unknown Exercise",
          relevance: `Related to: ${doc.metadata?.tags?.join(", ") || "fitness"}`,
        }))
        .slice(0, 3); // Limit to top 3

      return {
        response: chatResponse.output?.response || "I couldn't generate a response. Please try again.",
        conversationId: conversationId,
        suggestions: chatResponse.output?.suggestedFollowUps,
        relatedExercises: relatedExercises,
      };
    } catch (error) {
      console.error("Chat error:", error);
      return {
        response: "I'm having trouble understanding right now. Could you rephrase your question?",
        conversationId: input.conversationId,
        suggestions: [
          "How can I improve my workout routine?",
          "What exercises should I do for my goals?",
          "Can you explain proper form for an exercise?",
        ],
      };
    }
  }
);
