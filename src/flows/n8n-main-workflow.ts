/**
 * N8N Main Workflow - Exact Clone
 * Replicates the complete N8N workflow logic
 */

import {z} from "genkit";
import {ai} from "../genkit-config";
import {n8nWorkoutSummarizerFlow} from "./n8n-workout-summarizer";
import {n8nNextWorkoutCreatorFlow} from "./n8n-next-workout-creator";

/**
 * Main N8N workflow that handles the complete process
 * This is the webhook equivalent that processes workout requests
 */
export const n8nMainWorkflowFlow = ai.defineFlow(
  {
    name: "n8nMainWorkflow",
    inputSchema: z.object({
      // Webhook data - matches N8N webhook input
      userId: z.string(),
      
      // For workout summarization (if just completed a workout)
      justCompletedWorkout: z.object({
        plannedWorkout: z.object({
          workout_name: z.string(),
          exercises: z.array(z.object({
            name: z.string(),
            sets: z.number(),
            reps: z.array(z.number()),
            weight: z.array(z.number()),
            rest_periods: z.number(),
          })),
        }),
        actualWorkout: z.object({
          exercises: z.array(z.object({
            name: z.string(),
            actual_sets: z.array(z.object({
              order: z.number(),
              reps: z.number(),
              weight: z.number(),
              rep_difference: z.number(),
              set_feedback_difficulty: z.enum(["easy", "moderate", "hard"]),
            })),
          })),
        }),
        userWorkoutFeedback: z.string(),
        additionalMetrics: z.object({
          duration: z.number(),
          calories_burned: z.number().optional(),
          rating: z.number().min(1).max(5),
          notes: z.string().optional(),
        }),
        workoutDate: z.string(),
      }).optional(),
      
      // For next workout generation
      requestType: z.enum(["summarize_and_create_next", "create_next_only"]),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      workoutSummary: z.any().optional(),
      nextWorkout: z.object({
        workoutId: z.string(),
        workout_name: z.string(),
        exercises: z.array(z.object({
          name: z.string(),
          sets: z.number(),
          reps: z.array(z.number()),
          weight: z.array(z.number()),
          rest_interval: z.number(),
          order_index: z.number(),
        })),
        workout_rationale: z.string(),
      }).optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      let workoutSummary = null;
      let justFinishedWorkoutAiSummary = null;

      // Step 1: If user just completed a workout, summarize it first
      if (input.justCompletedWorkout && input.requestType === "summarize_and_create_next") {
        console.log("Processing workout summary...");
        
        const summaryResult = await n8nWorkoutSummarizerFlow({
          userId: input.userId,
          plannedWorkout: input.justCompletedWorkout.plannedWorkout,
          actualWorkout: input.justCompletedWorkout.actualWorkout,
          userWorkoutFeedback: input.justCompletedWorkout.userWorkoutFeedback,
          additionalMetrics: input.justCompletedWorkout.additionalMetrics,
          workoutDate: input.justCompletedWorkout.workoutDate,
        });

        if (!summaryResult.success) {
          return {
            success: false,
            message: `Failed to summarize workout: ${summaryResult.message}`,
          };
        }

        workoutSummary = summaryResult.aiSummary;
        justFinishedWorkoutAiSummary = summaryResult.aiSummary;

        // Wait 2 seconds (matching N8N wait node)
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // Step 2: Generate next workout
      console.log("Generating next workout...");
      
      const nextWorkoutResult = await n8nNextWorkoutCreatorFlow({
        userId: input.userId,
        justFinishedWorkoutAiSummary,
      });

      if (!nextWorkoutResult.success) {
        return {
          success: false,
          message: `Failed to create next workout: ${nextWorkoutResult.message}`,
        };
      }

      // Step 3: Return complete result (matching N8N aggregation)
      return {
        success: true,
        workoutSummary,
        nextWorkout: {
          workoutId: nextWorkoutResult.workoutId!,
          workout_name: nextWorkoutResult.nextWorkout!.workout_name,
          exercises: nextWorkoutResult.nextWorkout!.exercises,
          workout_rationale: nextWorkoutResult.workoutRationale!,
        },
        message: input.requestType === "summarize_and_create_next" 
          ? "Workout summarized and next workout created successfully!"
          : "Next workout created successfully!",
      };

    } catch (error) {
      console.error("Error in N8N main workflow:", error);
      return {
        success: false,
        message: `Workflow failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);

/**
 * Simplified flow for just creating next workout (no summarization)
 */
export const n8nCreateNextWorkoutOnlyFlow = ai.defineFlow(
  {
    name: "n8nCreateNextWorkoutOnly",
    inputSchema: z.object({
      userId: z.string(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      workoutId: z.string().optional(),
      nextWorkout: z.any().optional(),
      workoutRationale: z.string().optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    return await n8nNextWorkoutCreatorFlow({
      userId: input.userId,
      justFinishedWorkoutAiSummary: null,
    });
  }
);
