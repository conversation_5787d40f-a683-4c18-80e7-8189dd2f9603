# Workout Agent - AI-Powered Fitness Coach

An intelligent workout recommendation system built with Google Genkit, Firebase, and AI. This system provides personalized fitness guidance, workout generation, and real-time exercise recommendations.

## Features

- 🏋️ **Personalized Workout Generation** - Creates custom workouts based on user fitness level and goals
- 🤖 **AI Fitness Coach** - Chat with an intelligent fitness assistant for guidance
- 📊 **Workout Analysis** - Analyzes performance and provides insights
- 🎯 **Smart Exercise Recommendations** - Real-time exercise suggestions during workouts
- 📈 **Progressive Overload** - Automatically adjusts workouts for continuous improvement
- 💬 **Conversational Interface** - Natural language interaction with the fitness coach
- 🧠 **RAG-Enhanced Knowledge** - Retrieval-Augmented Generation for accurate fitness information

## Architecture

### Technology Stack
- **Google Genkit** - AI orchestration framework
- **Firebase Functions** - Serverless backend
- **Firestore** - Database for user data and workouts
- **Gemini 1.5 Flash** - AI model for intelligent responses
- **TypeScript** - Type-safe development

### Project Structure
```
workout_agent/
├── src/
│   ├── flows/               # Genkit AI flows
│   │   ├── onboarding-flows-v2.ts
│   │   ├── next-workout-flows-v2.ts
│   │   └── chat-flow.ts
│   ├── rag/                 # RAG implementation
│   │   ├── fitness-knowledge-base.ts  # Retrievers
│   │   ├── indexers.ts               # Document indexers
│   │   ├── sample-knowledge.ts       # Sample data
│   │   └── test-rag-flow.ts         # RAG testing
│   ├── callable-functions.ts # Firebase callable functions
│   ├── genkit-config.ts     # Genkit configuration
│   ├── workout-recommendation-agent.ts
│   └── index.ts             # Main entry point
├── prompts/                 # DotPrompt templates
│   ├── fitness-research.prompt
│   ├── first-workout.prompt
│   ├── workout-analysis.prompt
│   └── next-workout-creator.prompt
└── lib/                     # Compiled JavaScript
```

## Deployed Functions

All functions are deployed as Firebase Callable Functions with authentication:

1. **completeOnboarding** - Full onboarding flow (guide + workout)
2. **generateFitnessGuide** - Creates personalized fitness guide
3. **createFirstWorkout** - Generates initial workout plan
4. **recommendNextExercise** - Real-time exercise recommendations
5. **analyzeLastWorkout** - Performance analysis
6. **generateNextWorkout** - Progressive workout generation
7. **fitnessChat** - AI fitness coach chat

## Setup & Development

### Prerequisites
- Node.js 22+
- Firebase CLI
- Google Cloud Project with Gemini API enabled

### Installation
```bash
npm install
```

### Local Development
```bash
# Run Genkit Developer UI
npm run genkit

# Run specific flows
npm run genkit:onboarding
npm run genkit:next-workout

# Build
npm run build

# Deploy to Firebase
npm run deploy
```

### Environment Variables
Create a `.env` file:
```
GOOGLE_API_KEY=your-gemini-api-key
```

## Flutter Integration

See [FLUTTER_INTEGRATION_GUIDE.md](./FLUTTER_INTEGRATION_GUIDE.md) for detailed Flutter integration instructions.

Quick example:
```dart
final functions = FirebaseFunctions.instance;
final callable = functions.httpsCallable('completeOnboarding');
final result = await callable.call({'userId': userId});
```

## Database Schema

### Collections
- **users** - User profiles and preferences
- **exercises** - Exercise library
- **workoutHistory** - Completed workout logs
- **userWorkouts** - Saved workout templates
- **userFitnessGuides** - Personalized fitness guides
- **conversations** - Chat history
- **fitnessKnowledge** - RAG knowledge base for fitness information

## AI Capabilities

### Genkit Flows
1. **Onboarding Flow** - Analyzes user data to create personalized fitness plans
2. **Workout Generation** - Creates progressive workouts with proper periodization
3. **Exercise Recommendation** - Suggests exercises based on current workout context
4. **Performance Analysis** - Evaluates workout performance and recovery
5. **Fitness Chat** - Natural language Q&A about fitness with RAG enhancement
6. **Knowledge Indexing** - Index fitness documents into the RAG system

### Prompt Engineering
Uses DotPrompt templates for structured, consistent AI responses with:
- Type-safe schemas
- Contextual user data injection
- Output validation

### RAG (Retrieval-Augmented Generation)
Enhances AI responses with accurate fitness knowledge:
- **Retrievers**: Specialized retrievers for exercises, nutrition, and techniques
- **Indexers**: Document indexers for fitness content
- **Vector Search**: Semantic search using text embeddings
- **Knowledge Base**: Pre-built fitness knowledge including exercise techniques, nutrition guidelines, and recovery practices

## Testing

```bash
# Run ESLint
npm run lint

# Build TypeScript
npm run build

# Test locally with Genkit UI
npm run genkit

# Index sample fitness knowledge (run in Genkit UI)
# Execute the 'indexSampleFitnessData' flow

# Test RAG retrieval (run in Genkit UI)
# Execute the 'testRagRetrieval' flow
```

## Deployment

```bash
# Deploy all functions
firebase deploy --only functions

# Deploy specific function
firebase deploy --only functions:functionName
```

## Monitoring

- View function logs: `firebase functions:log`
- Genkit traces available in Firebase Console under "AI Logic"
- Function metrics in Firebase Console

## Security

- All functions require Firebase Authentication
- User data isolated by userId
- Input validation on all endpoints
- Secure API key management

## License

Private project - All rights reserved

---

Built with ❤️ using Google Genkit and Firebase