{"indexes": [{"collectionGroup": "workoutHistory", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "completedAt", "order": "DESCENDING"}]}, {"collectionGroup": "workoutHistory", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "workoutPlanId", "order": "ASCENDING"}]}, {"collectionGroup": "exercises", "queryScope": "COLLECTION", "fields": [{"fieldPath": "primaryMuscleGroup", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}]}, {"collectionGroup": "exercises", "queryScope": "COLLECTION", "fields": [{"fieldPath": "equipment", "arrayConfig": "CONTAINS"}, {"fieldPath": "difficulty", "order": "ASCENDING"}]}, {"collectionGroup": "userWorkouts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "userWorkouts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isPublic", "order": "ASCENDING"}]}, {"collectionGroup": "workoutPlans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "category", "order": "ASCENDING"}]}, {"collectionGroup": "workoutPlans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "duration", "order": "ASCENDING"}]}], "fieldOverrides": []}