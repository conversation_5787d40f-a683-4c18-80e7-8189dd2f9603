# Workout Agent - Function Tester

A comprehensive web application for testing all deployed Firebase callable functions in the Workout Agent project.

## 🚀 Features

- **Firebase Authentication**: Sign in with email/password (simulates mobile app auth)
- **Function Testing**: Test all 7 deployed callable functions
- **Real-time Results**: See function responses in real-time with proper formatting
- **User Profile Management**: Create sample profiles and check existing data
- **Batch Testing**: Test all functions in sequence with one click
- **Keyboard Shortcuts**: Quick access to common actions

## 📱 Tested Functions

### Core Functions
1. **Complete Onboarding** - Full onboarding flow (guide + workout)
2. **Generate Fitness Guide** - Creates personalized fitness guide
3. **Create First Workout** - Generates initial workout plan
4. **Recommend Next Exercise** - Real-time exercise recommendations
5. **Generate Next Workout** - Progressive workout generation
6. **Analyze Last Workout** - Performance analysis
7. **Fitness Chat** - AI fitness coach chat

### Utility Functions
- **Create Sample Profile** - Creates a test user profile in Firestore
- **Check User Profile** - Retrieves and displays current user data
- **Clear All Results** - Clears all function test results
- **Test All Functions** - Runs all functions in sequence

## 🛠️ Setup

### Prerequisites
- Modern web browser with JavaScript enabled
- Internet connection for Firebase services
- Email address for creating test account

### Running the Tester

1. **Open the tester**:
   ```bash
   # Navigate to the test folder
   cd test
   
   # Open index.html in your browser
   # You can use a simple HTTP server:
   python -m http.server 8000
   # or
   npx serve .
   ```

2. **Access the application**:
   - Open `http://localhost:8000` in your browser
   - Or simply open `index.html` directly in your browser

3. **Authenticate**:
   - **Real Auth**: Use pre-filled test account: `<EMAIL>` / `testpassword123`
   - **Create Account**: Make your own account with any email/password
   - **Mock Auth**: If APIs are blocked, click "Enable Mock Authentication"
   - The functions will create a user profile automatically

## 🎯 How to Use

### First Time Setup
1. **Sign in** with the test account or create your own
2. **Create a sample profile** using the utility button (recommended for testing)
3. **Start testing functions** - begin with "Complete Onboarding"

### Testing Individual Functions
1. Select any function card
2. Fill in optional parameters if needed
3. Click "Test Function"
4. View results in the expandable result section

### Testing All Functions
1. Click "Test All Functions" or press `Ctrl/Cmd + Enter`
2. Functions will be tested in sequence with 2-second delays
3. Monitor progress and results for each function

### Sample Test Flow
```
1. Create Sample Profile (utility)
2. Complete Onboarding
3. Generate Fitness Guide
4. Create First Workout
5. Recommend Next Exercise
6. Generate Next Workout
7. Fitness Chat (ask: "What's a good warm-up routine?")
8. Analyze Last Workout
```

## ⌨️ Keyboard Shortcuts

- `Ctrl/Cmd + Enter` - Test all functions in sequence
- `Ctrl/Cmd + R` - Clear all results (prevents browser refresh)

## 🔧 Configuration

The tester is pre-configured with your Firebase project settings:

```javascript
const firebaseConfig = {
    apiKey: "AIzaSyDG5SmBVzfEkNabJ6dy-shXGs1eXERmBoc",
    authDomain: "po2vf2ae7tal9invaj7jkf4a06hsac.firebaseapp.com",
    projectId: "po2vf2ae7tal9invaj7jkf4a06hsac",
    // ... other config
};
```

## 📊 Understanding Results

### Success Response
```json
{
  "success": true,
  "data": { /* function-specific data */ },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "error": "Error message",
  "code": "error-code",
  "details": { /* error details */ }
}
```

### Common Error Codes
- `unauthenticated` - User not signed in
- `permission-denied` - User doesn't have access
- `invalid-argument` - Missing or invalid parameters
- `not-found` - Requested resource doesn't exist

## 🧪 Mock Authentication Mode

If you encounter Firebase API blocking errors, the tester includes a **Mock Authentication Mode** that simulates all functionality without making real API calls.

### When to Use Mock Mode
- Firebase Identity Toolkit API is blocked
- Testing in restricted environments
- Demonstrating functionality without real backend
- Rapid prototyping and UI testing

### How to Enable Mock Mode
1. Open the tester web app
2. Scroll to the authentication section
3. Click "Enable Mock Authentication" button
4. Start testing functions immediately

### Mock Mode Features
- **Simulated Authentication**: Creates a mock user session
- **Realistic Responses**: Generates appropriate mock data for each function
- **Network Delays**: Simulates real API response times
- **Error Simulation**: Can simulate various error scenarios
- **Full UI Testing**: All buttons and features work normally

### Mock Response Examples
```json
{
  "success": true,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "mockMode": true,
  "userId": "mock-user-1705312200000",
  "message": "Mock response for testing purposes"
}
```

## 🐛 Troubleshooting

### API Blocked Errors
- **Problem**: "requests-to-this-api...are-blocked" errors
- **Solution 1**: Enable required APIs (see `fix-auth.md` for detailed steps)
- **Solution 2**: Use Mock Authentication Mode for testing
- **Quick Fix**: Run `./enable-apis.sh` (Mac/Linux) or `enable-apis.bat` (Windows)

### Authentication Issues
- **Problem**: "User not authenticated" errors
- **Solution**: Sign out and sign in again, check browser console for errors

### Function Call Failures
- **Problem**: Functions returning errors
- **Solution**: 
  1. Create a sample user profile first
  2. Check that all required fields are filled
  3. Ensure you're using the correct user ID

### No User Profile Found
- **Problem**: Functions fail because user profile doesn't exist
- **Solution**: Click "Create Sample Profile" before testing functions

### CORS Issues
- **Problem**: Network errors when calling functions
- **Solution**: 
  1. Ensure you're accessing via HTTP server (not file://)
  2. Check Firebase project configuration
  3. Verify function deployment status

## 📝 Sample Test Data

The sample user profile includes:
- **Age**: 34 years old
- **Gender**: Male
- **Height**: 175 cm
- **Weight**: 70 kg
- **Fitness Level**: Intermediate (cardio: 0.6, strength: 0.5)
- **Goals**: General fitness, muscle gain
- **Preferences**: 3 workouts/week, 45 minutes each
- **Environments**: Home and gym

## 🔍 Monitoring

### Browser Console
- Open Developer Tools (F12)
- Check Console tab for detailed logs
- All function calls and responses are logged

### Firebase Console
- Monitor function executions in Firebase Console
- Check function logs for server-side errors
- View AI traces in the "AI Logic" section

## 📱 Mobile App Integration

This tester simulates exactly how your mobile app would interact with the Firebase functions:

1. **Authentication**: Uses Firebase Auth with email/password (same as mobile)
2. **Function Calls**: Uses `httpsCallable` from Firebase SDK
3. **Data Format**: Same request/response format as mobile app
4. **Error Handling**: Same error codes and messages

## 🚀 Next Steps

After successful testing:

1. **Integrate with Mobile App**: Use the same patterns in your Flutter app
2. **Add More Test Cases**: Extend the tester with edge cases
3. **Performance Testing**: Monitor function execution times
4. **User Acceptance Testing**: Have real users test the functions

## 📞 Support

If you encounter issues:

1. Check the browser console for errors
2. Verify Firebase project configuration
3. Ensure all functions are properly deployed
4. Check function logs in Firebase Console

---

**Built with ❤️ for testing the Workout Agent Firebase Functions**
