@echo off
title Firebase Authentication API Enabler

echo 🔧 Firebase Authentication API Enabler
echo ======================================
echo.

set PROJECT_ID=po2vf2ae7tal9invaj7jkf4a06hsac

REM Check if gcloud is installed
gcloud --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Google Cloud CLI (gcloud) is not installed.
    echo.
    echo Please install it from: https://cloud.google.com/sdk/docs/install
    echo.
    echo After installation, run:
    echo   gcloud auth login
    echo   gcloud config set project %PROJECT_ID%
    echo   enable-apis.bat
    pause
    exit /b 1
)

echo ✅ Google Cloud CLI detected

REM Set the project
echo 🔧 Setting project to: %PROJECT_ID%
gcloud config set project %PROJECT_ID%

REM Check if project exists and we have access
gcloud projects describe %PROJECT_ID% >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Cannot access project %PROJECT_ID%
    echo    Make sure you have the right permissions
    pause
    exit /b 1
)

echo ✅ Project access confirmed
echo.

REM Enable required APIs
echo 🚀 Enabling required APIs...
echo.

echo 📡 Enabling Identity Toolkit API...
gcloud services enable identitytoolkit.googleapis.com
if %errorlevel% equ 0 (
    echo ✅ Identity Toolkit API enabled successfully
) else (
    echo ❌ Failed to enable Identity Toolkit API
)
echo.

echo 📡 Enabling Firebase API...
gcloud services enable firebase.googleapis.com
if %errorlevel% equ 0 (
    echo ✅ Firebase API enabled successfully
) else (
    echo ❌ Failed to enable Firebase API
)
echo.

echo 📡 Enabling Cloud Resource Manager API...
gcloud services enable cloudresourcemanager.googleapis.com
if %errorlevel% equ 0 (
    echo ✅ Cloud Resource Manager API enabled successfully
) else (
    echo ❌ Failed to enable Cloud Resource Manager API
)
echo.

echo 🔍 Verifying enabled APIs...
gcloud services list --enabled --filter="name:(identitytoolkit.googleapis.com OR firebase.googleapis.com)"

echo.
echo 🎉 API enablement complete!
echo.
echo 📋 Next steps:
echo 1. Wait 2-3 minutes for APIs to propagate
echo 2. Refresh your tester web page
echo 3. Try signing in again
echo 4. Should work now! 🚀
echo.
echo 🧪 Test with:
echo    Email: <EMAIL> (your existing user)
echo    Or create a new account in the tester

pause
