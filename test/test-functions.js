/**
 * Test Functions for Workout Agent
 * Functions to test all deployed Firebase callable functions
 */

// Utility function to show loading state
function setLoading(button, isLoading) {
    const spinner = button.querySelector('.loading');
    const playIcon = button.querySelector('.fa-play');
    
    if (isLoading) {
        spinner.classList.add('active');
        playIcon.style.display = 'none';
        button.disabled = true;
    } else {
        spinner.classList.remove('active');
        playIcon.style.display = 'inline';
        button.disabled = false;
    }
}

// Utility function to display results
function displayResult(button, result, isError = false) {
    const card = button.closest('.function-card');
    const resultContainer = card.querySelector('.result-container');
    const pre = resultContainer.querySelector('pre');
    
    resultContainer.classList.remove('hidden');
    pre.textContent = JSON.stringify(result, null, 2);
    pre.className = `p-3 rounded text-sm overflow-x-auto ${isError ? 'bg-red-100 text-red-800' : 'bg-gray-100'}`;
}

// Utility function to call Firebase function
async function callFunction(functionName, data, button) {
    if (!window.currentUser) {
        alert('Please sign in first');
        return;
    }

    setLoading(button, true);

    try {
        console.log(`Calling ${functionName} with data:`, data);

        if (window.mockAuthMode) {
            // Mock function call for testing when APIs are blocked
            console.log('Mock mode: Simulating function call');

            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

            // Generate mock response based on function name
            const mockResult = generateMockResponse(functionName, data);
            console.log(`${functionName} mock result:`, mockResult);
            displayResult(button, mockResult);

            return mockResult;
        } else {
            // Real Firebase function call
            const callable = window.functions.httpsCallable(functionName);
            const result = await callable(data);

            console.log(`${functionName} result:`, result);
            displayResult(button, result.data);

            return result.data;
        }
    } catch (error) {
        console.error(`${functionName} error:`, error);

        if (window.mockAuthMode) {
            // In mock mode, show a different error message
            displayResult(button, {
                error: 'Mock mode error simulation',
                code: 'mock-error',
                details: 'This is a simulated error for testing purposes',
                originalError: error.message
            }, true);
        } else {
            displayResult(button, {
                error: error.message,
                code: error.code,
                details: error.details
            }, true);
        }
        throw error;
    } finally {
        setLoading(button, false);
    }
}

// Generate mock responses for testing
function generateMockResponse(functionName, data) {
    const baseResponse = {
        success: true,
        timestamp: new Date().toISOString(),
        mockMode: true,
        userId: data.userId || window.currentUser.uid
    };

    switch (functionName) {
        case 'completeOnboarding':
            return {
                ...baseResponse,
                guideId: 'mock-guide-' + Date.now(),
                workoutId: 'mock-workout-' + Date.now(),
                message: 'Welcome to your fitness journey! Your personalized guide and first workout are ready. (Mock Response)'
            };

        case 'generateFitnessGuide':
            return {
                ...baseResponse,
                guideId: 'mock-guide-' + Date.now(),
                guide: {
                    title: 'Your Personalized Fitness Guide',
                    recommendations: [
                        'Start with 3 workouts per week',
                        'Focus on compound movements',
                        'Include cardio 2-3 times per week'
                    ],
                    goals: ['general_fitness', 'muscle_gain']
                },
                message: 'Personalized fitness guide generated successfully! (Mock Response)'
            };

        case 'createFirstWorkout':
            return {
                ...baseResponse,
                workoutId: 'mock-workout-' + Date.now(),
                workout: {
                    name: 'Your First Workout',
                    description: 'A beginner-friendly full-body workout',
                    exercises: [
                        { name: 'Push-ups', sets: 3, reps: 10, restSeconds: 60 },
                        { name: 'Squats', sets: 3, reps: 15, restSeconds: 60 },
                        { name: 'Plank', sets: 3, reps: 30, restSeconds: 45 }
                    ],
                    estimatedDuration: 30
                },
                message: 'Your first workout is ready! Remember to warm up and focus on form. (Mock Response)'
            };

        case 'recommendNextExercise':
            return {
                ...baseResponse,
                exercise: {
                    name: 'Jumping Jacks',
                    description: 'A great cardio exercise to get your heart rate up',
                    sets: 3,
                    reps: 20,
                    restSeconds: 45,
                    muscleGroups: ['full-body', 'cardio']
                },
                message: 'Here\'s your next exercise recommendation! (Mock Response)'
            };

        case 'generateNextWorkout':
            return {
                ...baseResponse,
                workoutId: 'mock-next-workout-' + Date.now(),
                workout: {
                    name: 'Progressive Strength Training',
                    description: 'Building on your previous workouts',
                    exercises: [
                        { name: 'Modified Push-ups', sets: 3, reps: 12, restSeconds: 60 },
                        { name: 'Goblet Squats', sets: 3, reps: 12, restSeconds: 60 },
                        { name: 'Mountain Climbers', sets: 3, reps: 20, restSeconds: 45 }
                    ],
                    estimatedDuration: 35,
                    progressionNotes: 'Increased reps and added new variations'
                },
                message: 'Your progressive workout is ready! (Mock Response)'
            };

        case 'analyzeLastWorkout':
            return {
                ...baseResponse,
                analysis: {
                    performance: 'Good',
                    strengths: ['Consistent form', 'Completed all sets'],
                    improvements: ['Could increase rest time', 'Focus on breathing'],
                    nextWorkoutSuggestions: ['Add 2 more reps', 'Try a new exercise variation'],
                    recoveryStatus: 'Well recovered'
                },
                message: 'Workout analysis complete! (Mock Response)'
            };

        case 'fitnessChat':
            return {
                ...baseResponse,
                response: `Thanks for your question: "${data.message}". This is a mock response from the AI fitness coach. In a real scenario, I would provide personalized fitness advice based on your profile and goals. (Mock Response)`,
                conversationId: data.conversationId || 'mock-conversation-' + Date.now(),
                message: 'AI response generated (Mock Response)'
            };

        default:
            return {
                ...baseResponse,
                message: `Mock response for ${functionName} function. This simulates a successful function call. (Mock Response)`
            };
    }
}

// Test Complete Onboarding
window.testCompleteOnboarding = async function() {
    const button = event.target;
    const userId = window.currentUser.uid;
    
    await callFunction('completeOnboarding', { userId }, button);
};

// Test Generate Fitness Guide
window.testGenerateFitnessGuide = async function() {
    const button = event.target;
    const userId = window.currentUser.uid;
    
    await callFunction('generateFitnessGuide', { userId }, button);
};

// Test Create First Workout
window.testCreateFirstWorkout = async function() {
    const button = event.target;
    const userId = window.currentUser.uid;
    const guideId = document.getElementById('guideId').value.trim() || undefined;
    
    const data = { userId };
    if (guideId) {
        data.guideId = guideId;
    }
    
    await callFunction('createFirstWorkout', data, button);
};

// Test Recommend Next Exercise
window.testRecommendNextExercise = async function() {
    const button = event.target;
    const userId = window.currentUser.uid;
    const saveWorkout = document.getElementById('saveWorkout').checked;
    
    await callFunction('recommendNextExercise', { 
        userId, 
        saveWorkout 
    }, button);
};

// Test Generate Next Workout
window.testGenerateNextWorkout = async function() {
    const button = event.target;
    const userId = window.currentUser.uid;
    const skipRecoveryCheck = document.getElementById('skipRecoveryCheck').checked;
    const targetMusclesInput = document.getElementById('targetMuscles').value.trim();
    const workoutType = document.getElementById('workoutType').value.trim() || undefined;
    
    const data = { userId };
    
    if (skipRecoveryCheck) {
        data.skipRecoveryCheck = true;
    }
    
    if (targetMusclesInput) {
        data.targetMuscles = targetMusclesInput.split(',').map(m => m.trim()).filter(m => m);
    }
    
    if (workoutType) {
        data.workoutType = workoutType;
    }
    
    await callFunction('generateNextWorkout', data, button);
};

// Test Analyze Last Workout
window.testAnalyzeLastWorkout = async function() {
    const button = event.target;
    const userId = window.currentUser.uid;
    const workoutId = document.getElementById('workoutId').value.trim() || undefined;
    
    const data = { userId };
    if (workoutId) {
        data.workoutId = workoutId;
    }
    
    await callFunction('analyzeLastWorkout', data, button);
};

// Test Fitness Chat
window.testFitnessChat = async function() {
    const button = event.target;
    const userId = window.currentUser.uid;
    const message = document.getElementById('chatMessage').value.trim();
    const conversationId = document.getElementById('conversationId').value.trim() || undefined;
    
    if (!message) {
        alert('Please enter a message');
        return;
    }
    
    const data = { userId, message };
    if (conversationId) {
        data.conversationId = conversationId;
    }
    
    await callFunction('fitnessChat', data, button);
};

// Utility function to create sample user profile
window.createSampleUserProfile = async function() {
    if (!window.currentUser) {
        alert('Please sign in first');
        return;
    }
    
    const button = event.target;
    setLoading(button, true);
    
    try {
        // Import Firebase Firestore
        const { getFirestore, doc, setDoc, Timestamp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const db = getFirestore();
        
        const userId = window.currentUser.uid;
        const userEmail = window.currentUser.email;
        
        // Sample user profile data
        const sampleProfile = {
            uid: userId,
            profile: {
                dateOfBirth: Timestamp.fromDate(new Date('1990-01-01')),
                gender: 'male',
                height: 175, // cm
                weight: 70, // kg
                preferredUnits: 'metric',
                age: 34
            },
            fitness: {
                cardioLevel: 0.6,
                strengthLevel: 0.5,
                flexibilityLevel: 0.4,
                goals: [
                    { type: 'general_fitness', priority: 1 },
                    { type: 'muscle_gain', priority: 2 }
                ],
                exercisesToAvoid: [],
                hasPersonalizedGuide: false
            },
            preferences: {
                workoutsPerWeek: 3,
                durationMinutes: 45,
                environments: ['home', 'gym'],
                theme: 'system',
                notifications: true
            },
            stats: {
                totalWorkouts: 0,
                totalMinutes: 0,
                totalCaloriesBurned: 0,
                currentStreak: 0,
                weeklyGoal: 180,
                monthlyStats: {}
            },
            email: userEmail,
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
            onboardingCompleted: false
        };
        
        await setDoc(doc(db, 'users', userId), sampleProfile);
        
        alert('Sample user profile created successfully!');
        console.log('Sample profile created:', sampleProfile);
        
    } catch (error) {
        console.error('Error creating sample profile:', error);
        alert('Failed to create sample profile: ' + error.message);
    } finally {
        setLoading(button, false);
    }
};

// Utility function to check user profile
window.checkUserProfile = async function() {
    if (!window.currentUser) {
        alert('Please sign in first');
        return;
    }
    
    const button = event.target;
    setLoading(button, true);
    
    try {
        // Import Firebase Firestore
        const { getFirestore, doc, getDoc } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const db = getFirestore();
        
        const userId = window.currentUser.uid;
        const userDoc = await getDoc(doc(db, 'users', userId));
        
        if (userDoc.exists()) {
            const userData = userDoc.data();
            displayResult(button, userData);
            console.log('User profile:', userData);
        } else {
            displayResult(button, { message: 'No user profile found. Create one first.' }, true);
        }
        
    } catch (error) {
        console.error('Error checking user profile:', error);
        displayResult(button, {
            error: error.message,
            message: 'Failed to check user profile'
        }, true);
    } finally {
        setLoading(button, false);
    }
};

// Utility function to clear all results
window.clearAllResults = function() {
    const resultContainers = document.querySelectorAll('.result-container');
    resultContainers.forEach(container => {
        container.classList.add('hidden');
    });
};

// Utility function to test all functions in sequence
window.testAllFunctions = async function() {
    if (!window.currentUser) {
        alert('Please sign in first');
        return;
    }
    
    if (!confirm('This will test all functions in sequence. This may take several minutes. Continue?')) {
        return;
    }
    
    const functions = [
        'testCompleteOnboarding',
        'testGenerateFitnessGuide', 
        'testCreateFirstWorkout',
        'testRecommendNextExercise',
        'testGenerateNextWorkout',
        'testAnalyzeLastWorkout'
    ];
    
    for (const funcName of functions) {
        try {
            console.log(`Testing ${funcName}...`);
            await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds between calls
            
            // Find the button for this function and simulate click
            const button = document.querySelector(`button[onclick="${funcName}()"]`);
            if (button) {
                await window[funcName]();
            }
        } catch (error) {
            console.error(`Error testing ${funcName}:`, error);
        }
    }
    
    alert('All function tests completed! Check the results below.');
};

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + Enter to test all functions
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        window.testAllFunctions();
    }
    
    // Ctrl/Cmd + R to clear all results
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        window.clearAllResults();
    }
});

console.log('Test functions loaded. Available functions:');
console.log('- testCompleteOnboarding()');
console.log('- testGenerateFitnessGuide()');
console.log('- testCreateFirstWorkout()');
console.log('- testRecommendNextExercise()');
console.log('- testGenerateNextWorkout()');
console.log('- testAnalyzeLastWorkout()');
console.log('- testFitnessChat()');
console.log('- createSampleUserProfile()');
console.log('- checkUserProfile()');
console.log('- clearAllResults()');
console.log('- testAllFunctions()');
console.log('');
console.log('Keyboard shortcuts:');
console.log('- Ctrl/Cmd + Enter: Test all functions');
console.log('- Ctrl/Cmd + R: Clear all results');
