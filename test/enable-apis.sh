#!/bin/bash

# Enable Firebase Authentication APIs
# This script enables the required Google Cloud APIs for Firebase Authentication

echo "🔧 Firebase Authentication API Enabler"
echo "======================================"
echo ""

PROJECT_ID="po2vf2ae7tal9invaj7jkf4a06hsac"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud CLI (gcloud) is not installed."
    echo ""
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    echo ""
    echo "After installation, run:"
    echo "  gcloud auth login"
    echo "  gcloud config set project $PROJECT_ID"
    echo "  ./enable-apis.sh"
    exit 1
fi

echo "✅ Google Cloud CLI detected"

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null 2>&1; then
    echo "❌ Not authenticated with Google Cloud"
    echo ""
    echo "Please run: gcloud auth login"
    exit 1
fi

ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1)
echo "✅ Authenticated as: $ACTIVE_ACCOUNT"

# Set the project
echo "🔧 Setting project to: $PROJECT_ID"
gcloud config set project $PROJECT_ID

# Check if project exists and we have access
if ! gcloud projects describe $PROJECT_ID > /dev/null 2>&1; then
    echo "❌ Cannot access project $PROJECT_ID"
    echo "   Make sure you have the right permissions"
    exit 1
fi

echo "✅ Project access confirmed"
echo ""

# Enable required APIs
echo "🚀 Enabling required APIs..."
echo ""

APIs=(
    "identitytoolkit.googleapis.com"
    "firebase.googleapis.com"
    "cloudresourcemanager.googleapis.com"
)

for api in "${APIs[@]}"; do
    echo "📡 Enabling $api..."
    if gcloud services enable $api; then
        echo "✅ $api enabled successfully"
    else
        echo "❌ Failed to enable $api"
    fi
    echo ""
done

echo "🔍 Verifying enabled APIs..."
echo ""

# Check which APIs are enabled
gcloud services list --enabled --filter="name:(identitytoolkit.googleapis.com OR firebase.googleapis.com)" --format="table(name,title)"

echo ""
echo "🎉 API enablement complete!"
echo ""
echo "📋 Next steps:"
echo "1. Wait 2-3 minutes for APIs to propagate"
echo "2. Refresh your tester web page"
echo "3. Try signing in again"
echo "4. Should work now! 🚀"
echo ""
echo "🧪 Test with:"
echo "   Email: <EMAIL> (your existing user)"
echo "   Or create a new account in the tester"
