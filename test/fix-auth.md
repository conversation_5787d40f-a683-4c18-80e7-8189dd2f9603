# Fix Firebase Authentication API Issues

## 🔧 Quick Fix for API Blocked Errors

The error `auth/requests-to-this-api-identitytoolkit-method-...are-blocked` means the required Google Cloud APIs are not enabled for your project.

### Step 1: Enable Required APIs in Google Cloud Console

1. **Open Google Cloud Console**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Make sure you're in the correct project: `po2vf2ae7tal9invaj7jkf4a06hsac`

2. **Enable Identity Toolkit API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Identity Toolkit API"
   - Click on it and click "ENABLE"
   - Wait for it to be enabled (may take 1-2 minutes)

3. **Enable Firebase Authentication API** (if not already enabled):
   - In the same library, search for "Firebase Authentication API"
   - Click on it and click "ENABLE"

### Step 2: Verify APIs are Enabled

1. **Check Enabled APIs**:
   - Go to "APIs & Services" > "Enabled APIs & services"
   - Look for:
     - ✅ Identity Toolkit API
     - ✅ Firebase Authentication API
     - ✅ Cloud Resource Manager API

### Step 3: Test Authentication

1. **Wait 2-3 minutes** after enabling APIs
2. **Refresh your tester page**
3. **Try signing in** with the test account
4. **Should work now!** 🎉

## 🚀 Alternative: Use Firebase CLI to Enable APIs

If you prefer command line:

```bash
# Install gcloud CLI if not already installed
# https://cloud.google.com/sdk/docs/install

# Login to Google Cloud
gcloud auth login

# Set your project
gcloud config set project po2vf2ae7tal9invaj7jkf4a06hsac

# Enable required APIs
gcloud services enable identitytoolkit.googleapis.com
gcloud services enable firebase.googleapis.com

# Verify APIs are enabled
gcloud services list --enabled | grep -E "(identity|firebase)"
```

## 🔍 Verify Your Setup

After enabling the APIs, you should be able to:

1. **Sign in with existing account**: `<EMAIL>` (from your Firebase console)
2. **Create new accounts** using the tester
3. **Call Firebase functions** without API errors

## 🐛 Still Having Issues?

### Check Project ID
Make sure you're in the correct Google Cloud project:
- Project ID: `po2vf2ae7tal9invaj7jkf4a06hsac`
- Project Name: Should match your Firebase project

### Check Billing
Some APIs require billing to be enabled:
1. Go to "Billing" in Google Cloud Console
2. Make sure billing is enabled for your project
3. Firebase has a generous free tier

### Check Permissions
Make sure you have the right permissions:
- You need to be Owner or Editor of the Google Cloud project
- Or have the "Firebase Admin" role

## ✅ Success Indicators

When everything is working:
- ✅ No more "API blocked" errors
- ✅ Can sign in with email/password
- ✅ Can create new accounts
- ✅ Can call Firebase functions
- ✅ See real responses (not mock)

## 🎯 Test Account Ready

Once APIs are enabled, use these test credentials:
- **Email**: `<EMAIL>` (your existing user)
- **Password**: Your actual password

Or create a new test account:
- **Email**: `<EMAIL>`
- **Password**: `testpassword123`

## 📞 Need Help?

If you're still having issues after enabling the APIs:

1. **Check the browser console** for detailed error messages
2. **Wait 5-10 minutes** after enabling APIs (propagation delay)
3. **Try incognito mode** to rule out browser cache issues
4. **Use Mock Mode** as a temporary workaround

---

**This should fix the authentication issues completely!** 🚀
