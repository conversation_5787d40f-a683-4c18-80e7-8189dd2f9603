# Firebase Authentication Setup

To use the email/password authentication in the tester, you need to enable it in your Firebase project.

## 🔧 Enable Required APIs and Authentication

### Step 1: Enable Identity Toolkit API

1. **Open Google Cloud Console**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Select your project: `po2vf2ae7tal9invaj7jkf4a06hsac`

2. **Enable Identity Toolkit API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Identity Toolkit API"
   - Click on it and click "Enable"
   - Wait for it to be enabled (may take a few minutes)

3. **Enable Firebase Authentication API**:
   - In the same library, search for "Firebase Authentication API"
   - Click on it and click "Enable"

### Step 2: Enable Email/Password Authentication

1. **Open Firebase Console**:
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Select your project: `po2vf2ae7tal9invaj7jkf4a06hsac`

2. **Navigate to Authentication**:
   - Click "Authentication" in the left sidebar
   - Click "Get started" if this is your first time

3. **Enable Email/Password Provider**:
   - Go to the "Sign-in method" tab
   - Find "Email/Password" in the list
   - Click on it and toggle "Enable"
   - Click "Save"

### Option 2: Firebase CLI (Alternative)

```bash
# Install Firebase CLI if not already installed
npm install -g firebase-tools

# Login to Firebase
firebase login

# Set your project
firebase use po2vf2ae7tal9invaj7jkf4a06hsac

# Note: Authentication providers must be enabled via the console
# The CLI doesn't support enabling auth providers directly
```

## 🎯 Test Account Setup

The tester comes with a pre-configured test account:

- **Email**: `<EMAIL>`
- **Password**: `testpassword123`

### Creating the Test Account

You have two options:

#### Option 1: Use the Tester (Recommended)
1. Open the tester web app
2. Scroll to the authentication section
3. Use the "Create Test Account" form
4. Enter any email and password you want

#### Option 2: Firebase Console
1. Go to Firebase Console > Authentication > Users
2. Click "Add user"
3. Enter email: `<EMAIL>`
4. Enter password: `testpassword123`
5. Click "Add user"

## 🔒 Security Notes

### For Testing
- The test account credentials are visible in the code
- This is fine for development and testing
- Don't use these credentials in production

### For Production
- Use strong, unique passwords
- Enable multi-factor authentication
- Set up proper security rules
- Monitor authentication logs

## 🚀 Quick Start

1. **Enable email/password auth** in Firebase Console
2. **Start the tester**: `cd test && npm start`
3. **Open browser**: `http://localhost:8080`
4. **Create account** or use test credentials
5. **Start testing** your functions!

## 🐛 Troubleshooting

### "auth/operation-not-allowed" Error
- **Problem**: Email/password authentication is not enabled
- **Solution**: Enable it in Firebase Console > Authentication > Sign-in method

### "auth/weak-password" Error
- **Problem**: Password is less than 6 characters
- **Solution**: Use a password with at least 6 characters

### "auth/email-already-in-use" Error
- **Problem**: Account with this email already exists
- **Solution**: Use the sign-in form instead of create account

### "auth/user-not-found" Error
- **Problem**: No account exists with this email
- **Solution**: Create an account first using the create account form

## 📱 Mobile App Integration

Once email/password auth is working in the tester, you can use the same approach in your mobile app:

### Flutter Example
```dart
import 'package:firebase_auth/firebase_auth.dart';

// Sign in
final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
  email: email,
  password: password,
);

// Create account
final credential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
  email: email,
  password: password,
);
```

### React Native Example
```javascript
import auth from '@react-native-firebase/auth';

// Sign in
const userCredential = await auth().signInWithEmailAndPassword(email, password);

// Create account
const userCredential = await auth().createUserWithEmailAndPassword(email, password);
```

## ✅ Verification

After enabling email/password authentication:

1. **Test sign up**: Create a new account in the tester
2. **Test sign in**: Sign in with the created account
3. **Test functions**: Try calling a Firebase function
4. **Check console**: Verify no authentication errors

If everything works, you're ready to integrate the same authentication flow into your mobile app!

---

**Need help?** Check the Firebase documentation: https://firebase.google.com/docs/auth
