{"name": "workout-agent-function-tester", "version": "1.0.0", "description": "Web application for testing Workout Agent Firebase callable functions", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "serve": "node server.js", "test": "echo \"Open http://localhost:8080 in your browser to test functions\" && npm start"}, "keywords": ["firebase", "functions", "testing", "workout", "fitness", "genkit"], "author": "Workout Agent Team", "license": "Private", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "."}, "bugs": {"url": "https://github.com/your-repo/issues"}, "homepage": "."}