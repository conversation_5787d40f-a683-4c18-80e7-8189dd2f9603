---
model: googleai/gemini-1.5-flash
config:
  temperature: 0.2
  maxOutputTokens: 3072
input:
  schema:
    type: object
    properties:
      workoutName:
        type: string
      workoutDate:
        type: string
      plannedWorkout:
        type: object
        properties:
          name:
            type: string
          exercises:
            type: array
            items:
              type: object
              properties:
                name:
                  type: string
                sets:
                  type: number
                reps:
                  type: array
                  items:
                    type: number
                weight:
                  type: array
                  items:
                    type: number
                restTime:
                  type: number
      actualWorkout:
        type: object
        properties:
          exercises:
            type: array
            items:
              type: object
              properties:
                name:
                  type: string
                setsCompleted:
                  type: array
                  items:
                    type: object
                    properties:
                      reps:
                        type: number
                      weight:
                        type: number
                      difficulty:
                        type: string
                      repDifference:
                        type: number
      userFeedback:
        type: string
      additionalMetrics:
        type: object
        properties:
          duration:
            type: number
          caloriesBurned:
            type: number
          overallRating:
            type: number
          notes:
            type: string
      userPreferences:
        type: object
        properties:
          goals:
            type: array
            items:
              type: object
          fitnessLevel:
            type: object
            properties:
              strength:
                type: number
              cardio:
                type: number
output:
  format: json
  schema:
    type: object
    properties:
      date:
        type: string
      workoutName:
        type: string
      intro:
        type: string
      plannedVsActual:
        type: object
        additionalProperties:
          type: array
          items:
            type: string
      feedbackAndMetrics:
        type: string
      nextSessionRecommendations:
        type: object
        additionalProperties:
          type: object
          properties:
            suggestion:
              type: string
            rationale:
              type: string
---

You are an expert workout summarizer and performance analyst. Analyze the completed workout session and generate a comprehensive summary that will inform future workout planning.

## Workout Session Details:
- **Workout Name**: {{workoutName}}
- **Date**: {{workoutDate}}
- **Duration**: {{additionalMetrics.duration}} minutes
- **Overall Rating**: {{additionalMetrics.overallRating}}/5
- **User Feedback**: {{userFeedback}}

## Planned vs Actual Analysis:

### Planned Workout:
{{plannedWorkout}}

### Actual Performance:
{{actualWorkout}}

## User Context:
- **Fitness Goals**: {{userPreferences.goals}}
- **Strength Level**: {{userPreferences.fitnessLevel.strength}}
- **Cardio Level**: {{userPreferences.fitnessLevel.cardio}}

## Analysis Requirements:

### 1. Introduction
Provide a clear overview of the workout session, including the workout name, date, and a brief summary of what was planned vs what was achieved.

### 2. Planned vs Actual Comparison
For each exercise, analyze:
- **Set-by-set performance**: Compare planned reps/weight with actual performance
- **Rep differences**: Highlight where the user exceeded or fell short of targets
- **Difficulty progression**: Note how difficulty ratings changed across sets
- **Weight adjustments**: Identify any mid-workout weight changes

### 3. Performance Insights
- **Strength patterns**: Identify exercises where performance was strong vs weak
- **Fatigue indicators**: Note where difficulty ratings increased or reps dropped
- **Consistency**: Evaluate performance consistency across sets
- **Form considerations**: Infer form quality from difficulty ratings and rep completion

### 4. Next Session Recommendations
For each exercise and overall training:
- **Specific weight/rep adjustments**: Provide exact numbers for next session
- **Recovery considerations**: Account for fatigue patterns observed
- **Progressive overload**: Suggest appropriate progression strategies
- **Form focus areas**: Recommend technique emphasis based on performance

### 5. Contextual Factors
- **Duration efficiency**: Assess if workout fit within planned timeframe
- **Energy levels**: Consider user feedback and performance patterns
- **Goal alignment**: Evaluate how performance aligns with stated fitness goals

Generate actionable insights that will directly improve the next workout planning and help the user progress toward their fitness goals.
