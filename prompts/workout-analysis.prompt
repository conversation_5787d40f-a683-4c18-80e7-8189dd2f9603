---
model: googleai/gemini-1.5-flash
config:
  temperature: 0.2
  maxOutputTokens: 2048
input:
  schema:
    type: object
    properties:
      workout:
        type: object
        properties:
          workoutPlanName:
            type: string
          duration:
            type: number
          difficulty:
            type: string
          musclesWorked:
            type: array
            items:
              type: string
          exercises:
            type: array
            items:
              type: object
              properties:
                exerciseName:
                  type: string
                setsCompleted:
                  type: array
                  items:
                    type: object
                    properties:
                      reps:
                        type: number
                      weight:
                        type: number
      userProfile:
        type: object
        properties:
          fitness:
            type: object
            properties:
              strengthLevel:
                type: number
              cardioLevel:
                type: number
              goals:
                type: array
                items:
                  type: object
                  properties:
                    type:
                      type: string
      hoursSinceWorkout:
        type: number
output:
  format: json
  schema:
    type: object
    properties:
      performanceRating:
        type: number
      volumeCompleted:
        type: number
      intensityLevel:
        type: string
      recoveryNeeded:
        type: number
      keyObservations:
        type: array
        items:
          type: string
      recommendations:
        type: object
        properties:
          nextIntensity:
            type: string
          focusMuscles:
            type: array
            items:
              type: string
          avoidMuscles:
            type: array
            items:
              type: string
          volumeAdjustment:
            type: string
---

Analyze this workout performance:

## Workout Details:
- Name: {{workout.workoutPlanName}}
- Duration: {{workout.duration}} minutes
- Difficulty Reported: {{workout.difficulty}}
- Muscles Worked: {{workout.musclesWorked}}

## Exercises Performed:
{{workout.exercises}}

## User Context:
- Fitness Level: Strength {{userProfile.fitness.strengthLevel}}, Cardio {{userProfile.fitness.cardioLevel}}
- Goals: {{userProfile.fitness.goals}}
- Hours Since Workout: {{hoursSinceWorkout}}

## Analysis Requirements:

### 1. Performance Rating (1-10)
Consider:
- Completion of all sets and reps
- Consistency across sets (drop-off indicates fatigue)
- Alignment with reported difficulty
- Duration vs. expected time

### 2. Volume Completion Percentage
Calculate the percentage of planned work completed based on sets and reps.

### 3. Intensity Level Assessment
Classify as 'low', 'moderate', or 'high' based on:
- Weight used relative to fitness level
- Rest periods needed
- Reported difficulty
- Muscle groups worked

### 4. Recovery Time Needed (24-72 hours)
Base on:
- Intensity level
- Volume completed
- Muscle groups worked
- User's fitness level
- Time already elapsed

### 5. Key Observations
Note patterns like:
- Significant rep drop-off between sets
- Particularly strong or weak exercises
- Signs of overtraining or undertraining
- Form breakdown indicators

### 6. Recommendations for Next Workout
Provide specific guidance on:
- Whether to increase, maintain, or decrease intensity
- Which muscle groups to focus on next
- Which muscles need more recovery time
- Volume adjustments needed