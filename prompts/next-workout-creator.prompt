---
model: googleai/gemini-1.5-flash
config:
  temperature: 0.3
  maxOutputTokens: 4096
input:
  schema:
    type: object
    properties:
      userProfile:
        type: object
        properties:
          fitness:
            type: object
            properties:
              strengthLevel:
                type: number
              cardioLevel:
                type: number
              goals:
                type: array
                items:
                  type: object
                  properties:
                    type:
                      type: string
          preferences:
            type: object
            properties:
              durationMinutes:
                type: number
      workoutHistory:
        type: object
        properties:
          totalWorkouts:
            type: number
          averageFrequency:
            type: number
          recentExercises:
            type: array
            items:
              type: object
              properties:
                exerciseId:
                  type: string
                name:
                  type: string
      lastWorkoutAnalysis:
        type: object
        properties:
          intensityLevel:
            type: string
          performanceRating:
            type: number
          recommendations:
            type: object
            properties:
              volumeAdjustment:
                type: string
              focusMuscles:
                type: array
                items:
                  type: string
              avoidMuscles:
                type: array
                items:
                  type: string
      hoursSinceLastWorkout:
        type: number
      targetMuscles:
        type: array
        items:
          type: string
      availableExercises:
        type: array
        items:
          type: object
          properties:
            id:
              type: string
            name:
              type: string
            primaryMuscleGroup:
              type: string
            difficulty:
              type: string
      workoutType:
        type: string
output:
  format: json
  schema:
    type: object
    properties:
      name:
        type: string
      description:
        type: string
      recommendedTiming:
        type: string
      exercises:
        type: array
        items:
          type: object
          properties:
            name:
              type: string
            sets:
              type: number
            reps:
              type: string
            weight:
              type: number
            restSeconds:
              type: number
            notes:
              type: string
      targetMuscles:
        type: array
        items:
          type: string
      difficulty:
        type: string
      rationale:
        type: string
---

Create the next workout based on this context:

## User Profile:
- Fitness Level: Strength {{userProfile.fitness.strengthLevel}}, Cardio {{userProfile.fitness.cardioLevel}}
- Goals: {{userProfile.fitness.goals}}
- Preferred Duration: {{userProfile.preferences.durationMinutes}} minutes

## Recent Training:
- Last {{workoutHistory.totalWorkouts}} workouts in 2 weeks
- Average frequency: {{workoutHistory.averageFrequency}}/week
- Hours since last workout: {{hoursSinceLastWorkout}}
- Last workout analysis: {{lastWorkoutAnalysis}}

## Muscle Group Planning:
Target Muscles: {{targetMuscles}}
Avoid Muscles (if any): {{lastWorkoutAnalysis.recommendations.avoidMuscles}}

## Available Exercises:
{{availableExercises}}

## Workout Requirements:

Create a workout that:
1. Respects recovery needs based on {{hoursSinceLastWorkout}} hours since last workout
2. Progresses appropriately from recent sessions
3. Targets underworked muscles while avoiding overworked ones
4. Fits within {{userProfile.preferences.durationMinutes}} minutes
5. Includes proper warmup considerations

Workout Type: {{workoutType}}

### Volume Guidelines:
- Volume adjustment recommendation: {{lastWorkoutAnalysis.recommendations.volumeAdjustment}}
- If increase: Add 1-2 sets per exercise OR increase reps by 2-3
- If decrease: Reduce sets by 1 per exercise OR decrease reps by 2-3
- If maintain: Keep current volume with slight progression
- Use moderate volume if no previous analysis available

### Intensity Guidelines (Fitness Level: {{userProfile.fitness.strengthLevel}}):
- Beginner (0-0.33): Focus on form, 2-3 sets, 12-15 reps, longer rest
- Intermediate (0.34-0.66): Balance volume/intensity, 3-4 sets, 8-12 reps
- Advanced (0.67+): Higher intensity, 4-5 sets, 6-10 reps, shorter rest
- Adjust based on current fitness level

### Exercise Selection:
- Prioritize compound movements for efficiency
- Order exercises from most to least demanding
- Include both bilateral and unilateral work
- Consider the available equipment in user's environment

Provide clear rationale explaining:
- Why this workout follows logically from their recent training
- How it addresses their primary goals
- Expected difficulty and outcomes
- When they should do this workout (today, tomorrow, etc.)