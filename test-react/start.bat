@echo off
title Workout Agent React Tester

echo 🏋️  Workout Agent React Tester
echo ==============================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    echo    Download from: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js detected

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm.
    pause
    exit /b 1
)

echo ✅ npm detected
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    echo.
    npm install
    echo.
)

echo 🚀 Starting React development server...
echo.
echo 📱 The app will open automatically in your browser
echo 🔧 Press Ctrl+C to stop the server
echo.

REM Start the React app
npm start

pause
