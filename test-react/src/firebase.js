// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFunctions } from "firebase/functions";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDa1rzUXjOAA9Eq-K3jz7n4X6bKpzGBHUc",
  authDomain: "po2vf2ae7tal9invaj7jkf4a06hsac.firebaseapp.com",
  projectId: "po2vf2ae7tal9invaj7jkf4a06hsac",
  storageBucket: "po2vf2ae7tal9invaj7jkf4a06hsac.firebasestorage.app",
  messagingSenderId: "745521622245",
  appId: "1:745521622245:web:effc5edd6209b8a99c780c"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Firebase Functions and get a reference to the service
export const functions = getFunctions(app);

export default app;
