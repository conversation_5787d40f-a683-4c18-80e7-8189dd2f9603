import React, { useState, useEffect } from 'react';
import { onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { httpsCallable } from 'firebase/functions';
import { auth, functions } from './firebase';
import { User, Mail, Lock, Play, Loader, CheckCircle, XCircle, LogOut } from 'lucide-react';


function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [authLoading, setAuthLoading] = useState(false);
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('testpassword123');
  const [isSignUp, setIsSignUp] = useState(false);
  const [functionResults, setFunctionResults] = useState({});
  const [functionLoading, setFunctionLoading] = useState({});

  // Auth state observer
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Sign in function
  const handleSignIn = async (e) => {
    e.preventDefault();
    setAuthLoading(true);
    
    try {
      if (isSignUp) {
        await createUserWithEmailAndPassword(auth, email, password);
        alert('Account created successfully!');
      } else {
        await signInWithEmailAndPassword(auth, email, password);
      }
    } catch (error) {
      console.error('Auth error:', error);
      alert(`Authentication failed: ${error.message}`);
    } finally {
      setAuthLoading(false);
    }
  };

  // Sign out function
  const handleSignOut = async () => {
    try {
      await signOut(auth);
      setFunctionResults({});
    } catch (error) {
      console.error('Sign out error:', error);
      alert(`Sign out failed: ${error.message}`);
    }
  };

  // Call Firebase function
  const callFunction = async (functionName, data = {}) => {
    if (!user) {
      alert('Please sign in first');
      return;
    }

    setFunctionLoading(prev => ({ ...prev, [functionName]: true }));
    
    try {
      const callable = httpsCallable(functions, functionName);
      const result = await callable({ userId: user.uid, ...data });
      
      setFunctionResults(prev => ({
        ...prev,
        [functionName]: {
          success: true,
          data: result.data,
          timestamp: new Date().toISOString()
        }
      }));
      
      console.log(`${functionName} result:`, result.data);
    } catch (error) {
      console.error(`${functionName} error:`, error);
      
      setFunctionResults(prev => ({
        ...prev,
        [functionName]: {
          success: false,
          error: error.message,
          code: error.code,
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setFunctionLoading(prev => ({ ...prev, [functionName]: false }));
    }
  };

  // Function test configurations
  const functionTests = [
    {
      name: 'completeOnboarding',
      title: 'Complete Onboarding',
      description: 'Creates a personalized fitness guide and first workout for new users.',
      color: 'bg-green-500',
      icon: '🎯'
    },
    {
      name: 'generateFitnessGuide',
      title: 'Generate Fitness Guide',
      description: 'Creates a personalized fitness guide based on user profile.',
      color: 'bg-blue-500',
      icon: '📚'
    },
    {
      name: 'createFirstWorkout',
      title: 'Create First Workout',
      description: 'Generates an initial workout plan for new users.',
      color: 'bg-purple-500',
      icon: '🏋️'
    },
    {
      name: 'recommendNextExercise',
      title: 'Recommend Next Exercise',
      description: 'Gets real-time exercise recommendations during workouts.',
      color: 'bg-yellow-500',
      icon: '💡'
    },
    {
      name: 'generateNextWorkout',
      title: 'Generate Next Workout',
      description: 'Creates progressive workouts based on workout history.',
      color: 'bg-indigo-500',
      icon: '⚡'
    },
    {
      name: 'analyzeLastWorkout',
      title: 'Analyze Last Workout',
      description: 'Analyzes performance and provides insights on completed workouts.',
      color: 'bg-red-500',
      icon: '📊'
    },
    {
      name: 'fitnessChat',
      title: 'Fitness Chat',
      description: 'Chat with the AI fitness coach for personalized advice.',
      color: 'bg-teal-500',
      icon: '💬',
      data: { message: 'What\'s a good warm-up routine?' }
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader className="animate-spin" size={24} />
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <span className="text-2xl mr-3">🏋️</span>
              <h1 className="text-3xl font-bold text-gray-900">Workout Agent Tester</h1>
            </div>
            {user && (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <User size={16} />
                  <span>{user.email}</span>
                </div>
                <button
                  onClick={handleSignOut}
                  className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <LogOut size={16} />
                  <span>Sign Out</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!user ? (
          // Authentication Form
          <div className="max-w-md mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {isSignUp ? 'Create Account' : 'Sign In'}
                </h2>
                <p className="text-gray-600">
                  {isSignUp ? 'Create a new account to test functions' : 'Sign in to test your Firebase functions'}
                </p>
              </div>

              <form onSubmit={handleSignIn} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter your password"
                      required
                    />
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={authLoading}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  {authLoading ? (
                    <Loader className="animate-spin" size={20} />
                  ) : (
                    <>
                      <span>{isSignUp ? 'Create Account' : 'Sign In'}</span>
                    </>
                  )}
                </button>
              </form>

              <div className="mt-6 text-center">
                <button
                  onClick={() => setIsSignUp(!isSignUp)}
                  className="text-blue-600 hover:text-blue-700 text-sm"
                >
                  {isSignUp ? 'Already have an account? Sign in' : 'Need an account? Sign up'}
                </button>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Quick Test Account</h4>
                <p className="text-sm text-blue-700">
                  Use the pre-filled credentials above for instant testing.
                </p>
              </div>
            </div>
          </div>
        ) : (
          // Function Testing Interface
          <div className="space-y-8">
            {/* User Info */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <User className="mr-2 text-blue-600" size={24} />
                User Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">User ID</label>
                  <input
                    type="text"
                    value={user.uid}
                    readOnly
                    className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="text"
                    value={user.email}
                    readOnly
                    className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                  <input
                    type="text"
                    value={user.displayName || 'Not set'}
                    readOnly
                    className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg"
                  />
                </div>
              </div>
            </div>

            {/* Function Tests */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {functionTests.map((test) => (
                <div key={test.name} className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className={`w-10 h-10 ${test.color} rounded-lg flex items-center justify-center text-white text-lg mr-3`}>
                      {test.icon}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{test.title}</h3>
                  </div>
                  
                  <p className="text-gray-600 mb-4">{test.description}</p>
                  
                  <button
                    onClick={() => callFunction(test.name, test.data)}
                    disabled={functionLoading[test.name]}
                    className={`w-full ${test.color} hover:opacity-90 disabled:opacity-50 text-white py-2 px-4 rounded-lg font-medium transition-opacity flex items-center justify-center space-x-2`}
                  >
                    {functionLoading[test.name] ? (
                      <Loader className="animate-spin" size={20} />
                    ) : (
                      <>
                        <Play size={16} />
                        <span>Test Function</span>
                      </>
                    )}
                  </button>

                  {/* Results */}
                  {functionResults[test.name] && (
                    <div className="mt-4">
                      <div className="flex items-center mb-2">
                        {functionResults[test.name].success ? (
                          <CheckCircle className="text-green-500 mr-2" size={16} />
                        ) : (
                          <XCircle className="text-red-500 mr-2" size={16} />
                        )}
                        <span className="font-medium text-gray-900">Result:</span>
                      </div>
                      <pre className={`text-sm p-3 rounded-lg overflow-x-auto ${
                        functionResults[test.name].success 
                          ? 'bg-green-50 text-green-800' 
                          : 'bg-red-50 text-red-800'
                      }`}>
                        {JSON.stringify(functionResults[test.name], null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Utility Actions */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Utility Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => setFunctionResults({})}
                  className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
                >
                  Clear All Results
                </button>
                <button
                  onClick={() => {
                    functionTests.forEach((test, index) => {
                      setTimeout(() => callFunction(test.name, test.data), index * 2000);
                    });
                  }}
                  className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
                >
                  Test All Functions
                </button>
                <button
                  onClick={() => console.log('Current results:', functionResults)}
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
                >
                  Log Results to Console
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
